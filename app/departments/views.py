from flask import render_template, request, jsonify
from app import db
from app.models import Hotel, Department, FinancialRecord, Employee
from app.main.utils import calculate_department_busy_level
import logging

logger = logging.getLogger(__name__)

# 定义部门解锁规则（与需求文档保持一致）
DEPARTMENT_RULES = {
    "前台部": {"level": 1, "cost": 0},
    "客房部": {"level": 1, "cost": 0},
    "人事部": {"level": 1, "cost": 0},  # 人事部默认解锁
    "营销部": {"level": 2, "cost": 500000},
    "餐饮部": {"level": 3, "cost": 1000000},
    "安保部": {"level": 4, "cost": 2000000},
    "财务部": {"level": 5, "cost": 5000000},
    "商务部": {"level": 6, "cost": 10000000},
    "工程部": {"level": 7, "cost": 30000000},
    "康养部": {"level": 8, "cost": 60000000},
    "董事会": {"level": 9, "cost": 100000000}
}

from app.departments import bp


def get_sorted_departments(hotel_id):
    """获取按等级排序的部门列表"""
    departments = Department.query.filter_by(hotel_id=hotel_id).all()
    
    # 按照解锁等级对部门进行排序
    def get_department_level(dept):
        return DEPARTMENT_RULES.get(dept.name, {}).get("level", 999)
    
    departments.sort(key=get_department_level)
    return departments


def check_department_unlock_conditions(hotel, department_name):
    """检查部门解锁条件"""
    if not hotel:
        return False, '酒店数据未初始化'
    
    department = Department.query.filter_by(name=department_name, hotel_id=hotel.id).first()
    if not department:
        return False, '部门不存在'
    
    if department.is_unlocked:
        return False, '部门已解锁'
    
    rule = DEPARTMENT_RULES.get(department_name)
    if not rule:
        return False, '部门解锁规则未定义'
    
    if hotel.level < rule["level"]:
        return False, f'需要{rule["level"]}级酒店才能解锁'
    
    unlock_cost = rule["cost"]
    
    if hotel.money < unlock_cost:
        return False, '资金不足'
    
    return True, (department, unlock_cost)


@bp.route('/management')
def management():
    """部门管理页面"""
    hotel = Hotel.query.first()
    if not hotel:
        return "酒店数据未初始化", 500
    
    # 获取部门数据并按等级排序
    departments = get_sorted_departments(hotel.id)
    
    # 为每个部门添加额外信息
    for department in departments:
        # 获取解锁规则
        rule = DEPARTMENT_RULES.get(department.name, {})
        department.required_level = rule.get("level", 1)
        department.unlock_cost = rule.get("cost", 0)
        
        # 检查是否可以解锁
        if not department.is_unlocked:
            # 检查酒店等级是否满足要求
            if hotel.level >= department.required_level:
                # 检查资金是否足够
                if hotel.money >= department.unlock_cost:
                    department.can_unlock = True
                else:
                    department.can_unlock = False
                    department.unlock_condition = f"需要{department.unlock_cost}资金"
            else:
                department.can_unlock = False
                department.unlock_condition = f"需要{department.required_level}级酒店"
        else:
            department.can_unlock = False
            department.unlock_condition = ""
    
    # 获取各部门员工数量
    department_employee_counts = {}
    for department in departments:
        count = Employee.query.filter_by(
            hotel_id=hotel.id, 
            department=department.name
        ).count()
        department_employee_counts[department.name] = count
    
    # 获取各部门繁忙度
    department_busy_levels = calculate_department_busy_level(hotel)
    
    return render_template('departments.html', 
                          hotel=hotel, 
                          all_departments=departments,
                          department_employee_counts=department_employee_counts,
                          department_busy_levels=department_busy_levels)


@bp.route('/unlock/<int:department_id>', methods=['POST'])
def unlock_department(department_id):
    """解锁部门"""
    hotel = Hotel.query.first()
    if not hotel:
        return jsonify({'success': False, 'message': '酒店数据未初始化'}), 500
    
    # 根据ID获取部门
    department = Department.query.filter_by(id=department_id, hotel_id=hotel.id).first()
    if not department:
        return jsonify({'success': False, 'message': '部门不存在'}), 400
    
    department_name = department.name
    
    # 检查解锁条件
    can_unlock, result = check_department_unlock_conditions(hotel, department_name)
    if not can_unlock:
        return jsonify({'success': False, 'message': result}), 400
    
    department, unlock_cost = result
    
    # 解锁部门
    department.is_unlocked = True
    
    # 扣除费用
    hotel.money -= unlock_cost
    
    # 记录财务记录
    financial_record = FinancialRecord(
        hotel_id=hotel.id,
        date=hotel.date,  # 使用游戏时间而不是系统时间
        type="部门解锁",
        description=f"解锁{department_name}部门",
        amount=-unlock_cost
    )
    db.session.add(financial_record)
    
    # 提交更改
    try:
        db.session.commit()
        return jsonify({'success': True, 'message': '部门解锁成功'})
    except Exception as e:
        db.session.rollback()
        logger.error(f"解锁部门时出错: {str(e)}")
        return jsonify({'success': False, 'message': '解锁部门时发生错误'}), 500


@bp.route('/department/<int:department_id>')
def department_detail(department_id):
    """部门详情页面"""
    hotel = Hotel.query.first()
    if not hotel:
        return "酒店数据未初始化", 500
    
    department = Department.query.filter_by(id=department_id, hotel_id=hotel.id).first()
    if not department:
        return "部门不存在", 404
    
    if not department.is_unlocked:
        return "部门未解锁", 403
    
    # 获取该部门的员工
    employees = Employee.query.filter_by(hotel_id=hotel.id, department=department.name).all()
    
    # 计算部门繁忙度
    busy_level = calculate_department_busy_level(hotel).get(department.name, 0)
    
    return render_template('department_detail.html',
                          hotel=hotel,
                          department=department,
                          employees=employees,
                          busy_level=busy_level)


@bp.route('/get_departments_list', methods=['GET'])
def get_departments_list():
    """获取部门列表API"""
    try:
        hotel = Hotel.query.first()
        if not hotel:
            return jsonify({"success": False, "message": "酒店数据未初始化"}), 500

        departments_data = get_sorted_departments(hotel.id)
        return jsonify({
            "success": True,
            "departments": departments_data
        })
    except Exception as e:
        logger.error(f"获取部门列表时出错: {e}")
        return jsonify({"success": False, "message": "获取部门列表失败"}), 500
from flask import render_template, request, jsonify
from app import db
from app.models import Hotel, Room, FinancialRecord, GameSetting
from app.main.utils import get_current_hotel
import logging

logger = logging.getLogger(__name__)

from app.rooms import bp


@bp.route('/management')
def management():
    """房间管理页面"""
    hotel = get_current_hotel()
    if not hotel:
        return "酒店数据未初始化", 500

    # 获取所有房间
    rooms = get_sorted_rooms(hotel.id)

    # 定义房间价格
    room_prices = {
        "单人间": 300,
        "标准间": 500,
        "大床房": 700,
        "家庭房": 1000,
        "商务间": 1500,
        "行政间": 2000,
        "豪华间": 3000,
        "总统套房": 5000,
        "皇家套房": 8000,
        "总统别墅": 15000,
        "皇宫套房": 30000
    }

    # 定义房间解锁要求
    room_requirements = {
        "单人间": 1,
        "标准间": 1,
        "大床房": 2,
        "家庭房": 2,
        "商务间": 3,
        "行政间": 3,
        "豪华间": 4,
        "总统套房": 5,
        "皇家套房": 6,
        "总统别墅": 7,
        "皇宫套房": 8
    }

    # 按房间类型分组
    rooms_by_type = {}
    for room_type in room_prices.keys():
        rooms_by_type[room_type] = [room for room in rooms if room.type == room_type]

    # 计算统计数据
    total_rooms = sum(room.count for room in rooms)
    occupied_rooms = int(total_rooms * 0.7)  # 假设70%入住率
    occupancy_rate = (occupied_rooms / total_rooms * 100) if total_rooms > 0 else 0
    monthly_maintenance = total_rooms * 100  # 每间房每月100元维护费

    # 计算各房型入住率
    room_occupancy_rates = {}
    for room_type in room_prices.keys():
        # 根据房间等级设置基础入住率
        base_rates = {
            "单人间": 85, "标准间": 80, "大床房": 75, "家庭房": 70,
            "商务间": 65, "行政间": 60, "豪华间": 55, "总统套房": 50,
            "皇家套房": 45, "总统别墅": 40, "皇宫套房": 35
        }
        room_occupancy_rates[room_type] = base_rates.get(room_type, 70)

    return render_template('rooms.html',
                         hotel=hotel,
                         rooms=rooms,
                         rooms_by_type=rooms_by_type,
                         room_prices=room_prices,
                         room_requirements=room_requirements,
                         room_occupancy_rates=room_occupancy_rates,
                         total_rooms=total_rooms,
                         occupied_rooms=occupied_rooms,
                         occupancy_rate=occupancy_rate,
                         monthly_maintenance=monthly_maintenance)


@bp.route('/build', methods=['POST'])
def build_room():
    """建设房间"""
    try:
        hotel = get_current_hotel()
        if not hotel:
            return jsonify({"success": False, "message": "酒店数据未初始化"}), 500

        data = request.get_json()
        room_type = data.get('room_type')
        quantity = data.get('quantity', 1)

        if not room_type:
            return jsonify({"success": False, "message": "房间类型不能为空"}), 400

        if not isinstance(quantity, int) or quantity <= 0:
            return jsonify({"success": False, "message": "房间数量必须是正整数"}), 400

        # 定义房间价格
        room_prices = {
            "单人间": 300, "标准间": 500, "大床房": 700, "家庭房": 1000,
            "商务间": 1500, "行政间": 2000, "豪华间": 3000, "总统套房": 5000,
            "皇家套房": 8000, "总统别墅": 15000, "皇宫套房": 30000
        }

        # 定义房间解锁要求
        room_requirements = {
            "单人间": 1, "标准间": 1, "大床房": 2, "家庭房": 2,
            "商务间": 3, "行政间": 3, "豪华间": 4, "总统套房": 5,
            "皇家套房": 6, "总统别墅": 7, "皇宫套房": 8
        }

        # 检查酒店等级是否满足要求
        required_level = room_requirements.get(room_type, 1)
        if hotel.level < required_level:
            return jsonify({"success": False, "message": f"需要{required_level}星酒店才能建设{room_type}"}), 400

        # 计算建设费用
        room_price = room_prices.get(room_type, 500)
        construction_cost = room_price * 10  # 建设成本是房间价格的10倍
        total_cost = construction_cost * quantity

        # 检查资金是否足够
        if hotel.money < total_cost:
            return jsonify({"success": False, "message": f"资金不足，需要¥{total_cost:,}"}), 400

        # 查找或创建房间记录
        room = Room.query.filter_by(hotel_id=hotel.id, type=room_type).first()
        if not room:
            room = Room(
                hotel_id=hotel.id,
                type=room_type,
                count=0,
                price=room_price
            )
            db.session.add(room)

        # 更新房间数量
        room.count += quantity

        # 扣除费用
        hotel.money -= total_cost

        # 记录财务记录
        financial_record = FinancialRecord(
            hotel_id=hotel.id,
            record_date=hotel.date,
            expense=total_cost,
            description=f"建设 {quantity} 间 {room_type}"
        )
        db.session.add(financial_record)

        # 使用安全的数据库操作
        from app.main.utils import safe_database_operation

        def commit_operation():
            db.session.commit()
            return True

        result = safe_database_operation(commit_operation)
        if result:
            return jsonify({"success": True, "message": f"成功建设 {quantity} 间 {room_type}，花费¥{total_cost:,}"})
        else:
            return jsonify({"success": False, "message": "建设房间失败，请重试"}), 500

    except Exception as e:
        db.session.rollback()
        logger.error(f"建设房间时出错: {e}")
        return jsonify({"success": False, "message": "建设房间失败"}), 500


@bp.route('/add_room', methods=['POST'])
def add_room():
    """添加房间"""
    try:
        hotel = get_current_hotel()
        if not hotel:
            return jsonify({"success": False, "message": "酒店数据未初始化"}), 500

        data = request.get_json()
        room_type = data.get('room_type')
        count = data.get('count', 1)

        if not room_type:
            return jsonify({"success": False, "message": "房间类型不能为空"}), 400

        if not isinstance(count, int) or count <= 0:
            return jsonify({"success": False, "message": "房间数量必须是正整数"}), 400

        # 检查房间类型是否已解锁
        room = Room.query.filter_by(hotel_id=hotel.id, type=room_type).first()
        if not room:
            return jsonify({"success": False, "message": f"请先解锁{room_type}"}), 400

        # 计算建设费用（根据优化需求文档：房间价格的10倍作为建设成本）
        room_prices = {
            "单人间": 300,
            "标准间": 500,
            "大床房": 700,
            "家庭房": 1000,
            "商务间": 1500,
            "行政间": 2000,
            "豪华间": 3000,
            "总统套房": 5000,
            "皇家套房": 8000,
            "总统别墅": 15000,
            "皇宫套房": 30000
        }

        room_price = room_prices.get(room_type, 500)
        room_construction_cost = room_price * 10  # 建设成本是房间价格的10倍
        total_cost = room_construction_cost * count

        # 检查资金是否足够
        if hotel.money < total_cost:
            return jsonify({"success": False, "message": "资金不足"}), 400

        # 更新房间数量
        room.count += count

        # 扣除费用
        hotel.money -= total_cost

        # 记录财务记录
        financial_record = FinancialRecord(
            hotel_id=hotel.id,
            record_date=hotel.date,
            expense=total_cost,
            description=f"购买 {count} 间 {room_type}"
        )
        db.session.add(financial_record)

        db.session.commit()

        return jsonify({"success": True, "message": f"成功添加 {count} 间 {room_type}"})
    except Exception as e:
        db.session.rollback()
        logger.error(f"添加房间时出错: {e}")
        return jsonify({"success": False, "message": "添加房间失败"}), 500


@bp.route('/unlock', methods=['POST'])
def unlock_room():
    """解锁房间类型"""
    try:
        hotel = get_current_hotel()
        if not hotel:
            return jsonify({"success": False, "message": "酒店数据未初始化"}), 500

        data = request.get_json()
        room_type = data.get('room_type')

        if not room_type:
            return jsonify({"success": False, "message": "房间类型不能为空"}), 400

        # 检查是否可以解锁该房间类型
        can_unlock, message = check_room_unlock_conditions(hotel, room_type)
        if not can_unlock:
            return jsonify({"success": False, "message": message}), 400

        # 获取解锁费用
        unlock_cost = 10000 * hotel.level

        # 检查资金是否足够
        if hotel.money < unlock_cost:
            return jsonify({"success": False, "message": "资金不足"}), 400

        # 创建房间
        new_room = Room(
            hotel_id=hotel.id,
            type=room_type,
            count=0,  # 初始数量为0
            price=calculate_room_price(room_type)
        )
        db.session.add(new_room)

        # 扣除费用
        hotel.money -= unlock_cost

        # 记录财务记录
        financial_record = FinancialRecord(
            hotel_id=hotel.id,
            record_date=hotel.date,
            expense=unlock_cost,
            description=f"解锁房间类型: {room_type}"
        )
        db.session.add(financial_record)

        db.session.commit()

        return jsonify({"success": True, "message": f"成功解锁 {room_type}"})
    except Exception as e:
        db.session.rollback()
        logger.error(f"解锁房间时出错: {e}")
        return jsonify({"success": False, "message": "解锁房间失败"}), 500


def get_sorted_rooms(hotel_id):
    """获取按类型排序的房间列表"""
    # 定义房间类型的顺序（按照解锁顺序）
    room_type_order = {
        "单人间": 1,
        "标准间": 2,
        "家庭房": 3,
        "商务间": 4,
        "行政间": 5,
        "豪华间": 6,
        "总统套房": 7,
        "皇家套房": 8,
        "总统别墅": 9,
        "皇宫套房": 10
    }
    
    rooms = Room.query.filter_by(hotel_id=hotel_id).all()
    
    # 按照预定义顺序排序
    rooms.sort(key=lambda r: room_type_order.get(r.type, 999))
    return rooms


def get_default_room_price(room_type):
    """获取默认房间价格"""
    # 定义房间价格
    room_prices = {
        "单人间": 300,
        "标准间": 500,
        "家庭房": 700,
        "商务间": 1000,
        "海景房": 1500,
        "豪华间": 2000,
        "家庭套房": 5000,
        "商务套房": 8000,
        "豪华套房": 15000,
        "总统套房": 30000,
        "皇家套房": 8000,
        "总统别墅": 15000,
        "皇宫套房": 30000
    }
    return room_prices.get(room_type, 300)


def get_room_unlock_requirements(hotel_level):
    """获取各等级酒店可解锁的房间类型"""
    # 定义房间解锁规则（星级酒店对应解锁的房间类型）
    room_unlock_rules = {
        1: ["单人间", "标准间"],
        2: ["家庭房"],
        3: ["商务间"],
        4: ["海景房"],
        5: ["豪华间"],
        6: ["家庭套房"],
        7: ["商务套房"],
        8: ["豪华套房"],
        9: ["总统套房"]
    }
    
    # 收集当前等级及以下所有可解锁的房间类型
    unlocked_rooms = []
    for level in range(1, hotel_level + 1):
        if level in room_unlock_rules:
            unlocked_rooms.extend(room_unlock_rules[level])
    
    return unlocked_rooms


def calculate_room_price(room_type):
    """计算房间价格"""
    # 获取数据库中的价格设置
    price_setting = GameSetting.query.filter_by(key=f'room_price_{room_type}').first()
    if price_setting:
        try:
            return int(price_setting.value)
        except (ValueError, TypeError):
            return get_default_room_price(room_type)
    return get_default_room_price(room_type)


def get_unlockable_rooms(hotel):
    """获取当前可解锁的房间类型"""
    # 定义房间解锁规则（星级酒店对应解锁的房间类型）
    room_unlock_rules = {
        1: ["单人间", "标准间"],
        2: ["家庭房"],
        3: ["商务间"],
        4: ["海景房"],
        5: ["豪华间"],
        6: ["家庭套房"],
        7: ["商务套房"],
        8: ["豪华套房"],
        9: ["总统套房"]
    }
    
    unlockable = []
    if hotel.level in room_unlock_rules:
        for room_type in room_unlock_rules[hotel.level]:
            # 检查是否已拥有该类型房间
            existing_room = Room.query.filter_by(hotel_id=hotel.id, type=room_type).first()
            if not existing_room:
                unlockable.append({
                    "type": room_type,
                    "cost": 10000 * hotel.level  # 解锁费用为10000*酒店等级
                })
    
    return unlockable


def check_room_unlock_conditions(hotel, room_type):
    """检查房间解锁条件"""
    # 定义房间解锁规则（星级酒店对应解锁的房间类型）
    room_unlock_rules = {
        1: ["单人间", "标准间"],
        2: ["家庭房"],
        3: ["商务间"],
        4: ["海景房"],
        5: ["豪华间"],
        6: ["家庭套房"],
        7: ["商务套房"],
        8: ["豪华套房"],
        9: ["总统套房"]
    }
    
    # 检查当前酒店等级是否可以解锁该房间类型
    if hotel.level not in room_unlock_rules or room_type not in room_unlock_rules[hotel.level]:
        return False, '该房间类型暂不可解锁'
    
    # 检查资金是否足够
    # 房间解锁费用规则：1星1万，2星2万，依此类推
    unlock_cost = 10000 * hotel.level
    if hotel.money < unlock_cost:
        return False, '资金不足'
    
    return True, unlock_cost  # 返回True和费用而不是只返回True

from flask import render_template, request, jsonify
from app.finance import bp
from app.models import Hotel, FinancialRecord, Employee, Department
from app.main.utils import get_current_hotel
from datetime import datetime
import calendar
from collections import defaultdict

def get_financial_summary(hotel):
    """获取财务摘要"""
    # 获取所有财务记录
    records = FinancialRecord.query.filter_by(hotel_id=hotel.id).all()
    
    total_income = sum(record.income for record in records)
    total_expense = sum(record.expense for record in records)
    total_profit = total_income - total_expense
    
    return total_income, total_expense, total_profit

def get_monthly_financial_records(hotel):
    """获取月度财务记录"""
    # 获取所有财务记录
    records = FinancialRecord.query.filter_by(hotel_id=hotel.id).all()
    
    # 按月份分组
    monthly_records = defaultdict(list)
    for record in records:
        month_key = record.record_date.strftime('%Y-%m')
        monthly_records[month_key].append(record)
    
    return dict(monthly_records)

def calculate_total_salaries(hotel):
    """计算员工总工资"""
    employees = Employee.query.filter_by(hotel_id=hotel.id).all()
    total_salaries = sum(emp.salary for emp in employees)
    return total_salaries, len(employees)

def get_departments_with_employee_counts(hotel):
    """获取带有员工数量的部门列表"""
    departments = Department.query.filter_by(hotel_id=hotel.id).all()
    
    # 为每个部门计算员工数量
    for dept in departments:
        employee_count = Employee.query.filter_by(
            hotel_id=hotel.id, 
            department=dept.name
        ).count()
        dept.employee_count = employee_count
    
    return departments

@bp.route('/management')
def management():
    """财务管理页面"""
    hotel = get_current_hotel()
    if not hotel:
        return "酒店数据未初始化", 500

    # 获取分页参数
    page = request.args.get('page', 1, type=int)
    per_page = 20

    # 获取财务记录（分页）
    financial_records_query = FinancialRecord.query.filter_by(hotel_id=hotel.id).order_by(FinancialRecord.record_date.desc())
    pagination = financial_records_query.paginate(page=page, per_page=per_page, error_out=False)
    financial_records = pagination.items

    # 计算日收入、支出、利润
    from app.models import Room
    rooms = Room.query.filter_by(hotel_id=hotel.id).all()

    # 计算日收入（房间收入）
    daily_income = 0
    for room in rooms:
        occupancy_rate = 0.7  # 假设70%入住率
        daily_income += room.count * room.price * occupancy_rate

    # 计算日支出（员工工资 + 维护费）
    employees = Employee.query.filter_by(hotel_id=hotel.id).all()
    daily_salary = sum(emp.salary for emp in employees) / 30  # 月薪转日薪
    daily_maintenance = sum(room.count for room in rooms) * 10  # 每间房每日10元维护费
    daily_expense = daily_salary + daily_maintenance

    # 计算日利润
    daily_profit = daily_income - daily_expense

    # 收入分析
    income_breakdown = {
        "房间收入": daily_income,
        "服务收入": daily_income * 0.1,  # 假设服务收入是房间收入的10%
        "其他收入": daily_income * 0.05   # 假设其他收入是房间收入的5%
    }

    # 支出分析
    expense_breakdown = {
        "员工工资": daily_salary,
        "房间维护": daily_maintenance,
        "水电费": daily_maintenance * 0.5,
        "营销费用": daily_income * 0.02,  # 假设营销费用是收入的2%
        "其他支出": daily_expense * 0.1
    }

    return render_template('finance.html',
                         hotel=hotel,
                         financial_records=financial_records,
                         pagination=pagination,
                         daily_income=int(daily_income),
                         daily_expense=int(daily_expense),
                         daily_profit=int(daily_profit),
                         income_breakdown={k: int(v) for k, v in income_breakdown.items()},
                         expense_breakdown={k: int(v) for k, v in expense_breakdown.items()})

# 为了保持向后兼容性，添加一个index路由别名
index = management
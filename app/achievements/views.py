from flask import render_template, request, jsonify
from app import db
from app.models import Hotel, Achievement, FinancialRecord, Employee
from app.main.utils import get_current_hotel

from app.achievements import bp


@bp.route('/management')
def management():
    """成就管理页面"""
    hotel = get_current_hotel()
    if not hotel:
        return render_template('error.html', message='酒店数据未初始化')
    
    # 获取所有成就并按分类组织
    achievements = Achievement.query.filter_by(hotel_id=hotel.id).all()
    
    # 初始化成就分组
    achievement_groups = {
        '财务成就': [],
        '员工成就': [],
        '发展成就': [],
        '经营成就': [],
        '特殊成就': []
    }
    
    # 定义各类成就名称列表
    achievement_categories = {
        '财务成就': [
            '初次盈利', '百万富翁', '千万富翁', '亿万富翁', '月入百万',
            '月入千万', '年度盈利王', '连续盈利', '财务大师', '投资专家'
        ],
        '员工成就': [
            '首位员工', '百人团队', '千人团队', '万人团队', '人才伯乐',
            '培训大师', '高薪一族', '人事专家', '员工满意', '团队建设'
        ],
        '发展成就': [
            '星光初现', '三星荣耀', '四海为家', '五星级别', '六六大顺',
            '七星高照', '八方来客', '九霄云外', '部门齐全', '房间帝国'
        ],
        '经营成就': [
            '客满为患', '满意服务', '声望卓著', '营销专家', '广告大王',
            '好评如潮', '生意兴隆', '稳定发展', '高端客户', '品牌价值'
        ],
        '特殊成就': [
            '时间管理大师', '存档专家', '探索者', '完美主义者', '长期经营',
            '快速发展', '节约大师', '平衡大师', '幸运之星', '挑战者'
        ]
    }
    
    # 将成就按分类组织
    for achievement in achievements:
        for category, achievements_list in achievement_categories.items():
            if achievement.name in achievements_list:
                achievement_groups[category].append(achievement)
                break  # 找到匹配后跳出循环
    
    return render_template('achievements.html',
                         hotel=hotel,
                         achievement_groups=achievement_groups)


@bp.route('/claim_reward/<int:achievement_id>', methods=['POST'])
def claim_reward(achievement_id):
    """领取成就奖励"""
    hotel = get_current_hotel()
    if not hotel:
        return jsonify({'success': False, 'message': '酒店数据未初始化'}), 500
    
    # 查找成就
    achievement = Achievement.query.filter_by(
        id=achievement_id, 
        hotel_id=hotel.id,
        achieved=True,
        reward_claimed=False
    ).first()
    
    if not achievement:
        return jsonify({'success': False, 'message': '成就不存在或奖励已领取'}), 404
    
    # 根据成就名称给予奖励
    reward_amount = 0
    if achievement.name == '初次盈利':
        reward_amount = 10000
    elif achievement.name == '百万富翁':
        reward_amount = 50000
    elif achievement.name == '千万富翁':
        reward_amount = 200000
    elif achievement.name == '亿万富翁':
        reward_amount = 1000000
    elif achievement.name == '首位员工':
        reward_amount = 20000
    elif achievement.name == '星光初现':
        reward_amount = 50000
    elif achievement.name == '三星荣耀':
        reward_amount = 100000
    elif achievement.name == '五星级别':
        reward_amount = 500000
    elif achievement.name == '部门齐全':
        reward_amount = 1000000
    
    # 发放奖励
    if reward_amount > 0:
        hotel.money += reward_amount
        # 记录财务记录，使用游戏时间作为记录日期
        financial_record = FinancialRecord(
            hotel_id=hotel.id,
            record_date=hotel.date,  # 使用游戏时间作为记录日期
            description=f'成就奖励: {achievement.name}',
            income=reward_amount
        )
        db.session.add(financial_record)
    
    # 标记奖励已领取
    achievement.reward_claimed = True
    
    try:
        db.session.commit()
        return jsonify({'success': True, 'message': f'成功领取奖励 ¥{reward_amount:,}'})
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': '奖励领取失败'}), 500


def init_achievements(hotel):
    """初始化默认成就"""
    achievements_data = [
        # 财务成就
        {'name': '初次盈利', 'description': '酒店首次实现盈利'},
        {'name': '百万富翁', 'description': '酒店资金达到100万元'},
        {'name': '千万富翁', 'description': '酒店资金达到1000万元'},
        {'name': '亿万富翁', 'description': '酒店资金达到1亿元'},
        {'name': '月入百万', 'description': '单月收入达到100万元'},
        {'name': '月入千万', 'description': '单月收入达到1000万元'},
        {'name': '年度盈利王', 'description': '年度总盈利达到5000万元'},
        {'name': '连续盈利', 'description': '连续30天实现盈利'},
        {'name': '财务大师', 'description': '年度总收入达到1亿元'},
        {'name': '投资专家', 'description': '累计支出达到5000万元'},
        
        # 员工成就
        {'name': '首位员工', 'description': '招聘第一名员工'},
        {'name': '百人团队', 'description': '拥有100名员工'},
        {'name': '千人团队', 'description': '拥有1000名员工'},
        {'name': '万人团队', 'description': '拥有10000名员工'},
        {'name': '人才伯乐', 'description': '招聘1000名员工'},
        {'name': '培训大师', 'description': '累计培训员工5000人次'},
        {'name': '高薪一族', 'description': '单月支付工资超过100万元'},
        {'name': '人事专家', 'description': '累计招聘员工5000人'},
        {'name': '员工满意', 'description': '员工满意度达到90%以上'},
        {'name': '团队建设', 'description': '所有部门都拥有至少50名员工'},
        
        # 发展成就
        {'name': '星光初现', 'description': '酒店达到2星'},
        {'name': '三星荣耀', 'description': '酒店达到3星'},
        {'name': '四海为家', 'description': '酒店达到4星'},
        {'name': '五星级别', 'description': '酒店达到5星'},
        {'name': '六六大顺', 'description': '酒店达到6星'},
        {'name': '七星高照', 'description': '酒店达到7星'},
        {'name': '八方来客', 'description': '酒店达到8星'},
        {'name': '九霄云外', 'description': '酒店达到9星'},
        {'name': '部门齐全', 'description': '解锁所有11个部门'},
        {'name': '房间帝国', 'description': '拥有10000间房间'},
        
        # 经营成就
        {'name': '客满为患', 'description': '入住率达到100%'},
        {'name': '满意服务', 'description': '客户满意度达到95%以上'},
        {'name': '声望卓著', 'description': '声望值达到100000'},
        {'name': '营销专家', 'description': '举办100次营销活动'},
        {'name': '广告大王', 'description': '投放50次广告'},
        {'name': '好评如潮', 'description': '连续7天客户满意度超过90%'},
        {'name': '生意兴隆', 'description': '连续30天盈利'},
        {'name': '稳定发展', 'description': '连续365天运营'},
        {'name': '高端客户', 'description': '单日收入超过100万元'},
        {'name': '品牌价值', 'description': '声望等级达到传奇酒店'},
        
        # 特殊成就
        {'name': '时间管理大师', 'description': '暂停时间100次'},
        {'name': '存档专家', 'description': '保存游戏100次'},
        {'name': '探索者', 'description': '查看所有管理页面'},
        {'name': '完美主义者', 'description': '达成所有其他成就'},
        {'name': '长期经营', 'description': '运营超过10年'},
        {'name': '快速发展', 'description': '1年内达到5星'},
        {'name': '节约大师', 'description': '连续30天支出低于1万元'},
        {'name': '平衡大师', 'description': '连续30天收支平衡'},
        {'name': '幸运之星', 'description': '遇到10次正面随机事件'},
        {'name': '挑战者', 'description': '遇到10次负面随机事件并克服'},
    ]
    
    # 创建成就记录
    for achievement_data in achievements_data:
        achievement = Achievement(
            hotel_id=hotel.id,
            name=achievement_data['name'],
            description=achievement_data['description'],
            achieved=False,
            reward_claimed=False  # 添加奖励领取状态字段
        )
        db.session.add(achievement)
    
    db.session.commit()
    logger.info(f"初始化了 {len(achievements_data)} 个成就")


def check_achievements(hotel):
    """检查并更新成就状态"""
    achievements = Achievement.query.filter_by(hotel_id=hotel.id).all()
    
    # 获取统计数据
    total_money = hotel.money
    total_employees = hotel.employee_count  # 使用酒店模型中的员工计数字段
    total_rooms = hotel.room_count  # 使用酒店模型中的客房计数字段
    unlocked_departments = hotel.unlocked_departments_count  # 使用酒店模型中的解锁部门计数
    total_departments = hotel.total_departments_count  # 使用酒店模型中的总部门计数
    
    for achievement in achievements:
        # 检查各个成就条件
        if achievement.name == '初次盈利' and not achievement.achieved:
            if hotel.profit > 0:
                achievement.achieved = True
                achievement.achieved_date = hotel.date
                
        elif achievement.name == '百万富翁' and not achievement.achieved:
            if total_money >= 1000000:
                achievement.achieved = True
                achievement.achieved_date = hotel.date
                
        elif achievement.name == '千万富翁' and not achievement.achieved:
            if total_money >= 10000000:
                achievement.achieved = True
                achievement.achieved_date = hotel.date
                
        elif achievement.name == '亿万富翁' and not achievement.achieved:
            if total_money >= 100000000:
                achievement.achieved = True
                achievement.achieved_date = hotel.date
                
        elif achievement.name == '月入百万' and not achievement.achieved:
            if hotel.monthly_income >= 1000000:
                achievement.achieved = True
                achievement.achieved_date = hotel.date
                
        elif achievement.name == '月入千万' and not achievement.achieved:
            if hotel.monthly_income >= 10000000:
                achievement.achieved = True
                achievement.achieved_date = hotel.date
                
        elif achievement.name == '年度盈利王' and not achievement.achieved:
            if hotel.annual_profit >= 100000000:
                achievement.achieved = True
                achievement.achieved_date = hotel.date
                
        elif achievement.name == '连续盈利' and not achievement.achieved:
            if hotel.consecutive_profit_months >= 3:
                achievement.achieved = True
                achievement.achieved_date = hotel.date
                
        elif achievement.name == '财务大师' and not achievement.achieved:
            if hotel.consecutive_profit_months >= 12:
                achievement.achieved = True
                achievement.achieved_date = hotel.date
                
        elif achievement.name == '投资专家' and not achievement.achieved:
            if hotel.investment_return_rate >= 20:
                achievement.achieved = True
                achievement.achieved_date = hotel.date
                
        elif achievement.name == '首位员工' and not achievement.achieved:
            if total_employees >= 1:
                achievement.achieved = True
                achievement.achieved_date = hotel.date
                
        elif achievement.name == '百人团队' and not achievement.achieved:
            if total_employees >= 100:
                achievement.achieved = True
                achievement.achieved_date = hotel.date
                
        elif achievement.name == '千人团队' and not achievement.achieved:
            if total_employees >= 1000:
                achievement.achieved = True
                achievement.achieved_date = hotel.date
                
        elif achievement.name == '万人团队' and not achievement.achieved:
            if total_employees >= 10000:
                achievement.achieved = True
                achievement.achieved_date = hotel.date
                
        elif achievement.name == '人才伯乐' and not achievement.achieved:
            if Employee.query.filter_by(hotel_id=hotel.id, is_outstanding=True).count() >= 100:
                achievement.achieved = True
                achievement.achieved_date = hotel.date
                
        elif achievement.name == '培训大师' and not achievement.achieved:
            if Employee.query.filter_by(hotel_id=hotel.id, is_trained=True).count() >= 1000:
                achievement.achieved = True
                achievement.achieved_date = hotel.date
                
        elif achievement.name == '高薪一族' and not achievement.achieved:
            if hotel.avg_salary >= 10000:  # 使用酒店模型中的平均工资字段
                achievement.achieved = True
                achievement.achieved_date = hotel.date
                
        elif achievement.name == '人事专家' and not achievement.achieved:
            if hotel.avg_satisfaction >= 90:  # 使用酒店模型中的平均满意度字段
                achievement.achieved = True
                achievement.achieved_date = hotel.date
                
        elif achievement.name == '员工满意' and not achievement.achieved:
            if hotel.avg_satisfaction >= 95:  # 使用酒店模型中的平均满意度字段
                achievement.achieved = True
                achievement.achieved_date = hotel.date
                
        elif achievement.name == '团队建设' and not achievement.achieved:
            if hotel.team_building_events >= 10:
                achievement.achieved = True
                achievement.achieved_date = hotel.date
                
        elif achievement.name == '星光初现' and not achievement.achieved:
            if hotel.stars >= 1:
                achievement.achieved = True
                achievement.achieved_date = hotel.date
                
        elif achievement.name == '三星荣耀' and not achievement.achieved:
            if hotel.stars >= 3:
                achievement.achieved = True
                achievement.achieved_date = hotel.date
                
        elif achievement.name == '四海为家' and not achievement.achieved:
            if hotel.stars >= 4:
                achievement.achieved = True
                achievement.achieved_date = hotel.date
                
        elif achievement.name == '五星级别' and not achievement.achieved:
            if hotel.stars >= 5:
                achievement.achieved = True
                achievement.achieved_date = hotel.date
                
        elif achievement.name == '六六大顺' and not achievement.achieved:
            if hotel.stars >= 6:
                achievement.achieved = True
                achievement.achieved_date = hotel.date
                
        elif achievement.name == '七星高照' and not achievement.achieved:
            if hotel.stars >= 7:
                achievement.achieved = True
                achievement.achieved_date = hotel.date
                
        elif achievement.name == '八方来客' and not achievement.achieved:
            if hotel.stars >= 8:
                achievement.achieved = True
                achievement.achieved_date = hotel.date
                
        elif achievement.name == '九霄云外' and not achievement.achieved:
            if hotel.stars >= 9:
                achievement.achieved = True
                achievement.achieved_date = hotel.date
                
        elif achievement.name == '部门齐全' and not achievement.achieved:
            if unlocked_departments == total_departments:
                achievement.achieved = True
                achievement.achieved_date = hotel.date
                
        elif achievement.name == '房间帝国' and not achievement.achieved:
            if total_rooms >= 1000:
                achievement.achieved = True
                achievement.achieved_date = hotel.date
                
        elif achievement.name == '客满为患' and not achievement.achieved:
            if hotel.room_occupancy_rate >= 100:
                achievement.achieved = True
                achievement.achieved_date = hotel.date
                
        elif achievement.name == '满意服务' and not achievement.achieved:
            if hotel.customer_satisfaction >= 90:
                achievement.achieved = True
                achievement.achieved_date = hotel.date
                
        elif achievement.name == '声望卓著' and not achievement.achieved:
            if hotel.reputation >= 100:
                achievement.achieved = True
                achievement.achieved_date = hotel.date
                
        elif achievement.name == '营销专家' and not achievement.achieved:
            if hotel.marketing_effectiveness >= 90:
                achievement.achieved = True
                achievement.achieved_date = hotel.date
                
        elif achievement.name == '广告大王' and not achievement.achieved:
            if hotel.advertising_return_rate >= 10:
                achievement.achieved = True
                achievement.achieved_date = hotel.date
                
        elif achievement.name == '好评如潮' and not achievement.achieved:
            if hotel.total_positive_reviews >= 1000:  # 使用更准确的字段名
                achievement.achieved = True
                achievement.achieved_date = hotel.date
                
        elif achievement.name == '生意兴隆' and not achievement.achieved:
            if hotel.monthly_income >= 10000000:
                achievement.achieved = True
                achievement.achieved_date = hotel.date
                
        elif achievement.name == '稳定发展' and not achievement.achieved:
            if hotel.stable_income_months >= 3:
                achievement.achieved = True
                achievement.achieved_date = hotel.date
                
        elif achievement.name == '高端客户' and not achievement.achieved:
            if hotel.high_end_customers >= 100:
                achievement.achieved = True
                achievement.achieved_date = hotel.date
                
        elif achievement.name == '品牌价值' and not achievement.achieved:
            if hotel.brand_value >= 100000000:
                achievement.achieved = True
                achievement.achieved_date = hotel.date
    
    try:
        db.session.commit()
    except Exception as e:
        db.session.rollback()
        print(f"更新成就时出错: {e}")
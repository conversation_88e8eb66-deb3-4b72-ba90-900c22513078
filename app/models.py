from flask_sqlalchemy import SQLAlchemy
from datetime import datetime, date
from app import db

class Hotel(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    level = db.Column(db.Integer, default=1)
    date = db.Column(db.Date, default=datetime.utcnow().date())
    money = db.Column(db.Integer, default=1000000)
    time_running = db.Column(db.<PERSON><PERSON>, default=True)
    days_elapsed = db.Column(db.Integer, default=0)
    satisfaction = db.Column(db.Float, default=50.0)  # 满意度
    reputation = db.Column(db.Integer, default=0)     # 声望值
    reputation_level = db.Column(db.Integer, default=1)  # 声望等级
    time_speed = db.Column(db.Integer, default=1)  # 时间速度，1为正常速度，2为2倍速度
    
    def __repr__(self):
        return f'<Hotel {self.name}>'

class Employee(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    hotel_id = db.Column(db.Integer, db.ForeignKey('hotel.id'), nullable=False)
    name = db.Column(db.String(50), nullable=False)
    department = db.Column(db.String(50), nullable=False)
    level = db.Column(db.String(20), nullable=False)  # 初级、中级、高级、特级
    base_salary = db.Column(db.Integer, nullable=False)
    salary = db.Column(db.Integer, nullable=False)
    hire_date = db.Column(db.Date, nullable=False, default=datetime.utcnow().date())
    years_worked = db.Column(db.Integer, nullable=False, default=0)
    
    hotel = db.relationship('Hotel', backref=db.backref('employees', lazy=True))
    
    @property
    def work_age(self):
        """计算工龄"""
        if self.hire_date:
            delta = datetime.utcnow().date() - self.hire_date
            return delta.days // 365
        return 0
    
    def __repr__(self):
        return f'<Employee {self.name}>'

class Department(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    hotel_id = db.Column(db.Integer, db.ForeignKey('hotel.id'), nullable=False)
    name = db.Column(db.String(50), nullable=False)
    is_unlocked = db.Column(db.Boolean, nullable=False, default=False)
    unlock_cost = db.Column(db.Integer, nullable=False, default=0)
    
    hotel = db.relationship('Hotel', backref=db.backref('departments', lazy=True))
    
    def __repr__(self):
        return f'<Department {self.name}>'

class Room(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    hotel_id = db.Column(db.Integer, db.ForeignKey('hotel.id'), nullable=False)
    type = db.Column(db.String(50), nullable=False)
    count = db.Column(db.Integer, nullable=False, default=0)
    price = db.Column(db.Integer, nullable=False, default=0)
    
    hotel = db.relationship('Hotel', backref=db.backref('rooms', lazy=True))
    
    def __repr__(self):
        return f'<Room {self.type}>'

class FinancialRecord(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    hotel_id = db.Column(db.Integer, db.ForeignKey('hotel.id'), nullable=False)
    record_date = db.Column(db.Date, nullable=False, default=datetime.utcnow().date())
    description = db.Column(db.String(200), nullable=False)
    income = db.Column(db.Integer, nullable=False, default=0)
    expense = db.Column(db.Integer, nullable=False, default=0)
    department = db.Column(db.String(50), nullable=True)
    
    hotel = db.relationship('Hotel', backref=db.backref('financial_records', lazy=True))
    
    def __repr__(self):
        return f'<FinancialRecord {self.description}>'

# 季节和事件模型
class SeasonalEffect(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    hotel_id = db.Column(db.Integer, db.ForeignKey('hotel.id'), nullable=False)
    season = db.Column(db.String(20), nullable=False)  # 春、夏、秋、冬
    year = db.Column(db.Integer, nullable=False)
    effect_type = db.Column(db.String(50), nullable=False)  # 入住率调整、特殊事件等
    effect_value = db.Column(db.Float, nullable=False)  # 影响值
    start_date = db.Column(db.Date, nullable=False)
    end_date = db.Column(db.Date, nullable=False)
    
    hotel = db.relationship('Hotel', backref=db.backref('seasonal_effects', lazy=True))

# 成就系统模型
class Achievement(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    hotel_id = db.Column(db.Integer, db.ForeignKey('hotel.id'), nullable=False)
    name = db.Column(db.String(100), nullable=False)
    description = db.Column(db.String(200), nullable=False)
    category = db.Column(db.String(50), nullable=False)  # 财务、员工、发展、经营、特殊
    condition_type = db.Column(db.String(50), nullable=False)  # money, reputation, days, etc.
    condition_value = db.Column(db.Integer, nullable=False)
    reward_reputation = db.Column(db.Integer, default=0)
    achieved = db.Column(db.Boolean, nullable=False, default=False)
    achieved_date = db.Column(db.Date, nullable=True)
    reward_claimed = db.Column(db.Boolean, nullable=False, default=False)  # 奖励是否已领取

    hotel = db.relationship('Hotel', backref=db.backref('achievements', lazy=True))

    def __repr__(self):
        return f'<Achievement {self.name}>'

# 游戏设置模型（用于存档）
class GameSetting(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    hotel_id = db.Column(db.Integer, db.ForeignKey('hotel.id'), nullable=False)
    key = db.Column(db.String(100), nullable=False)
    value = db.Column(db.Text, nullable=True)
    
    hotel = db.relationship('Hotel', backref=db.backref('settings', lazy=True))
    
    def __repr__(self):
        return f'<GameSetting {self.key}>'

# 随机事件模型
class RandomEvent(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    hotel_id = db.Column(db.Integer, db.ForeignKey('hotel.id'), nullable=False)
    event_type = db.Column(db.String(50), nullable=False)
    description = db.Column(db.String(200), nullable=False)
    start_date = db.Column(db.Date, nullable=False)
    end_date = db.Column(db.Date, nullable=False)
    effect_value = db.Column(db.Float, nullable=False)  # 影响值
    
    hotel = db.relationship('Hotel', backref=db.backref('events', lazy=True))
    
    def __repr__(self):
        return f'<RandomEvent {self.event_type}: {self.description}>'


# 营销活动模型
class MarketingCampaign(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    hotel_id = db.Column(db.Integer, db.ForeignKey('hotel.id'), nullable=False)
    campaign_id = db.Column(db.String(50), nullable=False)  # online_ads, tv_ads, etc.
    name = db.Column(db.String(100), nullable=False)
    cost = db.Column(db.Integer, nullable=False)
    effect = db.Column(db.Integer, nullable=False)  # 入住率提升百分比
    duration = db.Column(db.Integer, nullable=False)  # 持续天数
    started_at = db.Column(db.Date, nullable=False)
    ends_at = db.Column(db.Date, nullable=False)
    is_active = db.Column(db.Boolean, default=True)

    hotel = db.relationship('Hotel', backref=db.backref('marketing_campaigns', lazy=True))

    @property
    def remaining_days(self):
        if not self.is_active:
            return 0
        from datetime import datetime
        remaining = (self.ends_at - datetime.utcnow().date()).days
        return max(0, remaining)

    @property
    def progress(self):
        if not self.is_active:
            return 100
        total_duration = (self.ends_at - self.started_at).days
        elapsed = (datetime.utcnow().date() - self.started_at).days
        return min(100, (elapsed / total_duration) * 100) if total_duration > 0 else 100

    def __repr__(self):
        return f'<MarketingCampaign {self.name}>'

def initialize_game_data():
    """初始化游戏数据"""
    from app import db
    
    # 检查是否已存在酒店数据
    existing_hotel = Hotel.query.first()
    if existing_hotel:
        # 如果已存在酒店数据，检查是否需要更新成就表结构
        try:
            # 尝试查询成就表的reward_claimed字段
            Achievement.query.first()
        except Exception as e:
            # 如果出现异常，说明数据库结构需要更新
            # 添加reward_claimed字段到成就表
            db.engine.execute('ALTER TABLE achievement ADD COLUMN reward_claimed BOOLEAN DEFAULT FALSE')
            db.session.commit()
        return
    
    # 创建酒店，初始时间为1990年1月1日，初始为1星
    hotel = Hotel(name="红珊瑚大酒店", level=1, money=1000000, date=date(1990, 1, 1), days_elapsed=0, time_running=True, reputation=0)
    db.session.add(hotel)
    db.session.flush()
    
    # 创建初始房间（单人间和标准间各10个）
    room_types = ["单人间", "标准间"]
    room_prices = {
        "单人间": 300,
        "标准间": 500,
        "大床房": 700,
        "家庭房": 1000,
        "商务间": 1500,
        "行政间": 2000,
        "豪华间": 3000,
        "总统套房": 5000,
        "皇家套房": 8000,
        "总统别墅": 15000,
        "皇宫套房": 30000
    }
    
    for room_type in room_types:
        room = Room(hotel_id=hotel.id, type=room_type, count=10, price=room_prices.get(room_type, 300))
        db.session.add(room)
    
    # 创建所有部门
    departments = [
        {"name": "前台部", "is_unlocked": True, "unlock_cost": 0},
        {"name": "客房部", "is_unlocked": True, "unlock_cost": 0},
        {"name": "人事部", "is_unlocked": True, "unlock_cost": 0},  # 人事部默认解锁
        {"name": "营销部", "is_unlocked": False, "unlock_cost": 500000},
        {"name": "餐饮部", "is_unlocked": False, "unlock_cost": 1000000},
        {"name": "安保部", "is_unlocked": False, "unlock_cost": 2000000},
        {"name": "财务部", "is_unlocked": False, "unlock_cost": 5000000},
        {"name": "商务部", "is_unlocked": False, "unlock_cost": 10000000},
        {"name": "工程部", "is_unlocked": False, "unlock_cost": 30000000},
        {"name": "康养部", "is_unlocked": False, "unlock_cost": 60000000},
        {"name": "董事会", "is_unlocked": False, "unlock_cost": 100000000}
    ]
    
    for dept_data in departments:
        department = Department(
            hotel_id=hotel.id, 
            name=dept_data["name"], 
            is_unlocked=dept_data["is_unlocked"],
            unlock_cost=dept_data["unlock_cost"]
        )
        db.session.add(department)
    
    
    db.session.commit()
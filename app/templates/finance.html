{% extends "base.html" %}

{% block title %}财务管理 - 酒店管理系统{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">财务管理</h1>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">财务记录筛选</h6>
                </div>
                <div class="card-body">
                    <form id="filterForm" class="row g-3">
                        <div class="col-md-3">
                            <label for="yearSelect" class="form-label">年份</label>
                            <select class="form-select" id="yearSelect">
                                <option value="">全部年份</option>
                                {% for year in available_years %}
                                <option value="{{ year }}" {% if year == selected_year %}selected{% endif %}>{{ year }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="monthSelect" class="form-label">月份</label>
                            <select class="form-select" id="monthSelect">
                                <option value="">全部月份</option>
                                {% for month in range(1, 13) %}
                                {% set month_str = '{:02d}'.format(month) %}
                                <option value="{{ month_str }}" {% if month_str == selected_month %}selected{% endif %}>
                                    {{ month_str }}
                                </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="departmentSelect" class="form-label">部门</label>
                            <select class="form-select" id="departmentSelect">
                                <option value="">全部部门</option>
                                {% for department in departments %}
                                <option value="{{ department.name }}" {% if department.name == selected_department %}selected{% endif %}>
                                    {{ department.name }}
                                </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-3 align-self-end">
                            <button type="button" class="btn btn-primary" onclick="filterRecords()">
                                <i class="bi bi-funnel"></i> 筛选
                            </button>
                            <button type="button" class="btn btn-secondary" onclick="resetFilter()">
                                <i class="bi bi-arrow-clockwise"></i> 重置
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">财务记录</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover" id="financeTable">
                            <thead class="thead-dark">
                                <tr>
                                    <th>日期</th>
                                    <th>收入</th>
                                    <th>支出</th>
                                    <th>描述</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for record in financial_records %}
                                <tr>
                                    <td>{{ record.record_date.strftime('%Y-%m-%d') }}</td>
                                    <td class="text-success">
                                        {% if record.income > 0 %}
                                            ¥{{ "{:,}".format(record.income) }}
                                        {% endif %}
                                    </td>
                                    <td class="text-danger">
                                        {% if record.expense > 0 %}
                                            ¥{{ "{:,}".format(record.expense) }}
                                        {% endif %}
                                    </td>
                                    <td>{{ record.description }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="d-flex align-items-center">
                            <div class="me-3">
                                <label for="perPageSelect" class="form-label me-2">每页显示</label>
                                <select class="form-select d-inline-block w-auto" id="perPageSelect">
                                    <option value="10" {% if per_page == 10 %}selected{% endif %}>10条</option>
                                    <option value="20" {% if per_page == 20 %}selected{% endif %}>20条</option>
                                    <option value="50" {% if per_page == 50 %}selected{% endif %}>50条</option>
                                    <option value="100" {% if per_page == 100 %}selected{% endif %}>100条</option>
                                </select>
                            </div>
                            <div>
                                显示第 {{ (current_page - 1) * per_page + 1 }} 到 
                                {{ [current_page * per_page, total_records] | min }} 条记录，
                                共 {{ total_records }} 条记录
                            </div>
                        </div>
                        <nav aria-label="财务记录分页">
                            <ul class="pagination mb-0">
                                {% if current_page > 1 %}
                                <li class="page-item">
                                    <a class="page-link" href="#" onclick="changePage({{ current_page - 1 }})">上一页</a>
                                </li>
                                {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link">上一页</span>
                                </li>
                                {% endif %}
                                
                                {% for page_num in range([1, current_page - 3] | max, [total_pages + 1, current_page + 4] | min) %}
                                    {% if page_num == current_page %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ page_num }}</span>
                                    </li>
                                    {% else %}
                                    <li class="page-item">
                                        <a class="page-link" href="#" onclick="changePage({{ page_num }})">{{ page_num }}</a>
                                    </li>
                                    {% endif %}
                                {% endfor %}
                                
                                {% if current_page < total_pages %}
                                <li class="page-item">
                                    <a class="page-link" href="#" onclick="changePage({{ current_page + 1 }})">下一页</a>
                                </li>
                                {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link">下一页</span>
                                </li>
                                {% endif %}
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    function filterRecords() {
        const year = document.getElementById('yearSelect').value;
        const month = document.getElementById('monthSelect').value;
        const department = document.getElementById('departmentSelect').value;
        const perPage = document.getElementById('perPageSelect').value;
        
        // 构建查询参数
        const params = new URLSearchParams();
        if (year) params.append('year', year);
        if (month) params.append('month', month);
        if (department) params.append('department', department);
        if (perPage && perPage !== '20') params.append('per_page', perPage); // 默认值为20，避免URL冗余
        
        // 重定向到带查询参数的URL
        window.location.href = '/finance/management?' + params.toString();
    }
    
    function resetFilter() {
        document.getElementById('yearSelect').value = '';
        document.getElementById('monthSelect').value = '';
        document.getElementById('departmentSelect').value = '';
        document.getElementById('perPageSelect').value = '20';
        window.location.href = '/finance/management';
    }
    
    function changePage(page) {
        const url = new URL(window.location);
        url.searchParams.set('page', page);
        window.location.href = url.toString();
    }
    
    // 监听每页显示条数的变化
    document.getElementById('perPageSelect').addEventListener('change', function() {
        filterRecords();
    });
</script>
{% endblock %}
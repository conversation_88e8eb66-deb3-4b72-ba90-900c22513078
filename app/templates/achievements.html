{% extends "base.html" %}

{% block title %}成就系统 - {{ hotel.name }}{% endblock %}

{% block content %}
<div class="row mb-3">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <h2><i class="bi bi-trophy-fill text-warning me-2"></i>成就系统</h2>
            <a href="{{ url_for('main.index') }}" class="btn btn-outline-secondary">
                <i class="bi bi-house-fill me-1"></i>返回首页
            </a>
        </div>
    </div>
</div>

<!-- 成就概况 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <h5 class="card-title mb-3">
                    <i class="bi bi-graph-up text-primary me-2"></i>成就概况
                </h5>
                <div class="row text-center">
                    {% set total_achievements = achievement_groups.values() | sum(attribute='|length') | sum %}
                    {% set achieved_count = achievement_groups.values() | sum(attribute='|selectattr("achieved")|list|length') | sum %}
                    <div class="col-md-3">
                        <div class="bg-success bg-opacity-10 rounded p-3">
                            <h4 class="text-success mb-1">{{ achieved_count }}</h4>
                            <small class="text-muted">已解锁成就</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="bg-primary bg-opacity-10 rounded p-3">
                            <h4 class="text-primary mb-1">{{ total_achievements }}</h4>
                            <small class="text-muted">成就总数</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="bg-warning bg-opacity-10 rounded p-3">
                            <h4 class="text-warning mb-1">{{ "%.1f"|format((achieved_count / total_achievements * 100) if total_achievements > 0 else 0) }}%</h4>
                            <small class="text-muted">完成度</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="bg-info bg-opacity-10 rounded p-3">
                            <h4 class="text-info mb-1">{{ achieved_count * 100 }}</h4>
                            <small class="text-muted">获得声望</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 成就分类 -->
{% for category, achievements in achievement_groups.items() %}
{% if achievements %}
<div class="row mb-4">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <h5 class="card-title mb-3">
                    {% if category == '财务成就' %}
                    <i class="bi bi-cash-stack text-success me-2"></i>
                    {% elif category == '员工成就' %}
                    <i class="bi bi-people-fill text-info me-2"></i>
                    {% elif category == '发展成就' %}
                    <i class="bi bi-arrow-up-circle text-primary me-2"></i>
                    {% elif category == '经营成就' %}
                    <i class="bi bi-graph-up text-warning me-2"></i>
                    {% else %}
                    <i class="bi bi-star-fill text-secondary me-2"></i>
                    {% endif %}
                    {{ category }}
                </h5>
                
                <div class="row">
                    {% for achievement in achievements %}
                    <div class="col-lg-4 col-md-6 mb-3">
                        <div class="card h-100 {% if achievement.achieved %}border-success bg-success bg-opacity-10{% else %}border-secondary{% endif %}">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-start mb-2">
                                    <h6 class="card-title mb-0">
                                        {% if achievement.achieved %}
                                        <i class="bi bi-trophy-fill text-warning me-1"></i>
                                        {% else %}
                                        <i class="bi bi-trophy text-muted me-1"></i>
                                        {% endif %}
                                        {{ achievement.name }}
                                    </h6>
                                    {% if achievement.achieved %}
                                    <span class="badge bg-success">已解锁</span>
                                    {% else %}
                                    <span class="badge bg-secondary">未解锁</span>
                                    {% endif %}
                                </div>
                                
                                <p class="text-muted small mb-2">{{ achievement.description }}</p>
                                
                                {% if achievement.achieved %}
                                <div class="mb-2">
                                    <small class="text-muted">解锁时间：</small>
                                    <span class="text-success">{{ achievement.achieved_date.strftime('%Y-%m-%d') if achievement.achieved_date else '未知' }}</span>
                                </div>
                                <div class="mb-3">
                                    <small class="text-muted">奖励：</small>
                                    <span class="text-warning">+100 声望值</span>
                                </div>
                                
                                {% if not achievement.reward_claimed %}
                                <div class="d-grid">
                                    <button class="btn btn-warning btn-sm" onclick="claimReward({{ achievement.id }})">
                                        <i class="bi bi-gift me-1"></i>领取奖励
                                    </button>
                                </div>
                                {% else %}
                                <div class="d-grid">
                                    <button class="btn btn-success btn-sm" disabled>
                                        <i class="bi bi-check me-1"></i>已领取
                                    </button>
                                </div>
                                {% endif %}
                                {% else %}
                                <div class="mb-3">
                                    <small class="text-muted">奖励：</small>
                                    <span class="text-muted">+100 声望值</span>
                                </div>
                                <div class="d-grid">
                                    <button class="btn btn-secondary btn-sm" disabled>
                                        <i class="bi bi-lock me-1"></i>未解锁
                                    </button>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endfor %}

{% if not achievement_groups or not any(achievement_groups.values()) %}
<div class="row">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-body text-center py-5">
                <i class="bi bi-trophy display-1 text-muted"></i>
                <h4 class="text-muted mt-3">暂无成就</h4>
                <p class="text-muted">成就系统正在初始化中...</p>
                <button class="btn btn-primary" onclick="location.reload()">
                    <i class="bi bi-arrow-clockwise me-1"></i>刷新页面
                </button>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block scripts %}
<script>
function claimReward(achievementId) {
    if (confirm('确定要领取这个成就的奖励吗？')) {
        apiRequest(`/achievements/claim_reward/${achievementId}`, {
            method: 'POST'
        }).then(data => {
            if (data.success) {
                showMessage(data.message, 'success');
                setTimeout(() => location.reload(), 1000);
            } else {
                showMessage(data.message, 'error');
            }
        }).catch(error => {
            showMessage('领取奖励失败', 'error');
        });
    }
}
</script>
{% endblock %}

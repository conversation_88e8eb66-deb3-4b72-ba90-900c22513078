{% extends "base.html" %}

{% block title %}成就系统 - 酒店管理系统{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 页面标题 -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">成就系统</h1>
    </div>

    <!-- 成就列表按分类展示 -->
    {% for group_name, achievements in achievement_groups.items() %}
    <div class="row">
        <div class="col-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">{{ group_name }}</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover table-sm">
                            <thead class="thead-dark">
                                <tr>
                                    <th style="font-size: 0.85rem; width: 15%;">成就名称</th>
                                    <th style="font-size: 0.85rem; width: 25%;">描述</th>
                                    <th style="font-size: 0.85rem; width: 10%;">状态</th>
                                    <th style="font-size: 0.85rem; width: 15%;">达成日期</th>
                                    <th style="font-size: 0.85rem; width: 20%;">奖励</th>
                                    <th style="font-size: 0.85rem; width: 15%;">操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for achievement in achievements %}
                                <tr class="{% if achievement.achieved %}table-success{% endif %}" style="font-size: 0.85rem; height: 40px;">
                                    <td style="height: 40px; vertical-align: middle;">{{ achievement.name }}</td>
                                    <td style="height: 40px; vertical-align: middle;">{{ achievement.description }}</td>
                                    <td style="height: 40px; vertical-align: middle; text-align: center;">
                                        {% if achievement.achieved %}
                                            <span class="badge bg-success">已达成</span>
                                        {% else %}
                                            <span class="badge bg-secondary">未达成</span>
                                        {% endif %}
                                    </td>
                                    <td style="height: 40px; vertical-align: middle;">
                                        {% if achievement.achieved_date %}
                                            {{ achievement.achieved_date.strftime('%Y-%m-%d') }}
                                        {% else %}
                                            -
                                        {% endif %}
                                    </td>
                                    <td style="height: 40px; vertical-align: middle;">
                                        {% if achievement.name in ['初次盈利', '百万富翁', '千万富翁', '亿万富翁'] %}
                                            +100声望
                                        {% elif achievement.name in ['星光初现', '三星荣耀', '四海为家', '五星级别', '六六大顺', '七星高照', '八方来客', '九霄云外'] %}
                                            +200声望
                                        {% elif achievement.name in ['首位员工', '百人团队', '千人团队', '万人团队'] %}
                                            +150声望
                                        {% elif achievement.name in ['部门齐全', '房间帝国'] %}
                                            +500声望
                                        {% elif achievement.name in ['客满为患', '满意服务', '声望卓著', '营销专家', '广告大王'] %}
                                            +300声望
                                        {% elif achievement.name in ['好评如潮', '生意兴隆', '稳定发展', '高端客户', '品牌价值'] %}
                                            +400声望
                                        {% elif achievement.name in ['月入百万', '年度盈利王', '连续盈利', '财务大师', '投资专家'] %}
                                            +250声望
                                        {% elif achievement.name in ['人才伯乐', '培训大师', '高薪一族', '人事专家', '员工满意', '团队建设'] %}
                                            +180声望
                                        {% elif achievement.name in ['时间管理大师', '存档专家', '探索者', '完美主义者', '长期经营', '快速发展', '节约大师', '平衡大师', '幸运之星', '挑战者'] %}
                                            +100声望
                                        {% else %}
                                            +50声望
                                        {% endif %}
                                    </td>
                                    <td style="height: 40px; vertical-align: middle; text-align: center;">
                                        {% if achievement.achieved and not achievement.reward_claimed %}
                                            <button class="btn btn-sm btn-success" onclick="claimReward({{ achievement.id }})" style="padding: 2px 8px; font-size: 0.8rem;">
                                                领取奖励
                                            </button>
                                        {% elif achievement.achieved and achievement.reward_claimed %}
                                            <span class="text-muted">已领取</span>
                                        {% else %}
                                            <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endfor %}
</div>

<script>
    function claimReward(achievementId) {
        fetch(`/achievements/claim_reward/${achievementId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('奖励领取成功！');
                location.reload();
            } else {
                alert('奖励领取失败: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('领取奖励时发生错误');
        });
    }
</script>
{% endblock %}
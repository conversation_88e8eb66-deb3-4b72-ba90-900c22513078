{% extends "base.html" %}

{% block title %}酒店升级 - {{ hotel.name }}{% endblock %}

{% block content %}
<div class="row mb-3">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <h2><i class="bi bi-arrow-up-circle-fill text-primary me-2"></i>酒店升级</h2>
            <a href="{{ url_for('main.index') }}" class="btn btn-outline-secondary">
                <i class="bi bi-house-fill me-1"></i>返回首页
            </a>
        </div>
    </div>
</div>

<!-- 当前酒店状态 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <h5 class="card-title mb-3">
                    <i class="bi bi-star-fill text-warning me-2"></i>当前酒店状态
                </h5>
                <div class="row text-center">
                    <div class="col-md-3">
                        <div class="bg-warning bg-opacity-10 rounded p-3">
                            <h4 class="text-warning mb-1">{{ hotel.level }}星</h4>
                            <small class="text-muted">当前等级</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="bg-success bg-opacity-10 rounded p-3">
                            <h4 class="text-success mb-1">¥{{ "{:,}".format(hotel.money) }}</h4>
                            <small class="text-muted">当前资金</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="bg-info bg-opacity-10 rounded p-3">
                            <h4 class="text-info mb-1">{{ hotel.reputation }}</h4>
                            <small class="text-muted">声望值</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="bg-primary bg-opacity-10 rounded p-3">
                            <h4 class="text-primary mb-1">{{ hotel.days_elapsed }}天</h4>
                            <small class="text-muted">运营天数</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 升级路径 -->
<div class="row">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <h5 class="card-title mb-3">
                    <i class="bi bi-ladder text-primary me-2"></i>升级路径
                </h5>

                <div class="table-responsive">
                    <table class="table table-hover align-middle">
                        <thead class="table-light">
                            <tr>
                                <th width="5%">状态</th>
                                <th width="15%">等级</th>
                                <th width="15%">升级费用</th>
                                <th width="15%">资金要求</th>
                                <th width="15%">声望要求</th>
                                <th width="15%">满意度要求</th>
                                <th width="10%">天数要求</th>
                                <th width="10%">操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for level, requirements in upgrade_requirements.items() %}
                            <tr class="{% if level <= hotel.level %}table-success{% elif can_upgrade.get(level, False) %}table-warning{% endif %}">
                                <td>
                                    {% if level <= hotel.level %}
                                    <i class="bi bi-check-circle-fill text-success fs-5"></i>
                                    {% elif can_upgrade.get(level, False) %}
                                    <i class="bi bi-arrow-up-circle text-warning fs-5"></i>
                                    {% else %}
                                    <i class="bi bi-lock-fill text-secondary fs-5"></i>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <strong>{{ level }}星酒店</strong>
                                        {% if level <= hotel.level %}
                                        <span class="badge bg-success ms-2">已达成</span>
                                        {% elif can_upgrade.get(level, False) %}
                                        <span class="badge bg-warning ms-2">可升级</span>
                                        {% else %}
                                        <span class="badge bg-secondary ms-2">未满足</span>
                                        {% endif %}
                                    </div>
                                </td>
                                <td>
                                    {% if level <= hotel.level %}
                                    <span class="text-success">已支付</span>
                                    {% else %}
                                    <span class="text-warning fw-bold">¥{{ "{:,}".format(requirements.cost) }}</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if level <= hotel.level %}
                                    <span class="text-success">✓</span>
                                    {% elif hotel.money >= requirements.cost %}
                                    <span class="text-success">✓ ¥{{ "{:,}".format(hotel.money) }}</span>
                                    {% else %}
                                    <span class="text-danger">✗ ¥{{ "{:,}".format(hotel.money) }}</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if level <= hotel.level %}
                                    <span class="text-success">✓</span>
                                    {% elif hotel.reputation >= requirements.reputation %}
                                    <span class="text-success">✓ {{ hotel.reputation }}</span>
                                    {% else %}
                                    <span class="text-danger">✗ {{ hotel.reputation }}/{{ requirements.reputation }}</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if level <= hotel.level %}
                                    <span class="text-success">✓</span>
                                    {% elif hotel.satisfaction >= requirements.satisfaction %}
                                    <span class="text-success">✓ {{ "%.1f"|format(hotel.satisfaction) }}</span>
                                    {% else %}
                                    <span class="text-danger">✗ {{ "%.1f"|format(hotel.satisfaction) }}/{{ requirements.satisfaction }}</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if level <= hotel.level %}
                                    <span class="text-success">✓</span>
                                    {% elif hotel.days_elapsed >= requirements.days %}
                                    <span class="text-success">✓ {{ hotel.days_elapsed }}</span>
                                    {% else %}
                                    <span class="text-danger">✗ {{ hotel.days_elapsed }}/{{ requirements.days }}</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if level <= hotel.level %}
                                    <span class="text-success">已完成</span>
                                    {% elif can_upgrade.get(level, False) %}
                                    <button class="btn btn-sm btn-warning" onclick="upgradeHotel({{ level }})">
                                        <i class="bi bi-arrow-up"></i>
                                    </button>
                                    {% else %}
                                    <button class="btn btn-sm btn-secondary" disabled>
                                        <i class="bi bi-lock"></i>
                                    </button>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function upgradeHotel(level) {
    if (confirm(`确定要升级到${level}星酒店吗？这将花费大量资金！`)) {
        apiRequest('/hotel/upgrade', {
            method: 'POST',
            body: JSON.stringify({level: level})
        }).then(data => {
            if (data.success) {
                showMessage(data.message, 'success');
                setTimeout(() => location.reload(), 1500);
            } else {
                showMessage(data.message, 'error');
            }
        }).catch(error => {
            showMessage('升级失败', 'error');
        });
    }
}
</script>
{% endblock %}

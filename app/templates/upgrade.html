{% extends "base.html" %}

{% block title %}酒店升星 - 酒店管理系统{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">酒店升星</h1>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">升星列表</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover">
                            <thead class="thead-dark">
                                <tr>
                                    <th>目标星级</th>
                                    <th>升星费用</th>
                                    <th>声望要求</th>
                                    <th>解锁部门</th>
                                    <th>解锁房间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for level in range(1, 10) %}
                                <tr class="{% if hotel.level >= level %}table-success{% elif hotel.level == level - 1 %}table-warning{% endif %}">
                                    <td>{{ level }} 星</td>
                                    <td>
                                        {% if level == 1 %}
                                        -
                                        {% else %}
                                        ¥{{ "{:,}".format(level_costs[level|string].cost if level_costs[level|string] else level * 500000) }}
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if level == 1 %}
                                        -
                                        {% else %}
                                        {{ "{:,}".format(level_costs[level|string].reputation if level_costs[level|string] else level * 5000) }} 点
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if level == 1 %}
                                        <ul class="mb-0">
                                            <li>前台部 <span class="badge bg-success">已解锁</span></li>
                                            <li>客房部 <span class="badge bg-success">已解锁</span></li>
                                        </ul>
                                        {% else %}
                                        <ul class="mb-0">
                                            {% for department in level_departments[level|string] %}
                                            <li>
                                                {{ department }} 
                                                {% if department_status[department] %}
                                                <span class="badge bg-success">已解锁</span>
                                                {% else %}
                                                <span class="badge bg-secondary">未解锁</span>
                                                {% endif %}
                                            </li>
                                            {% endfor %}
                                        </ul>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <ul class="mb-0">
                                            {% for room in level_rooms[level|string] %}
                                            <li>{{ room }}</li>
                                            {% endfor %}
                                        </ul>
                                    </td>
                                    <td>
                                        {% if level == 1 %}
                                            <span class="text-muted">初始星级</span>
                                        {% elif level == hotel.level + 1 %}
                                            {% if hotel.money >= (level_costs[level|string].cost if level_costs[level|string] else level * 500000) and hotel.reputation >= (level_costs[level|string].reputation if level_costs[level|string] else level * 5000) %}
                                            <button class="btn btn-sm btn-success" onclick="upgradeToLevel({{ level }})">
                                                <i class="bi bi-arrow-up-circle"></i> 升星
                                            </button>
                                            {% elif hotel.money < (level_costs[level|string].cost if level_costs[level|string] else level * 500000) %}
                                            <span class="text-muted">资金不足</span>
                                            {% elif hotel.reputation < (level_costs[level|string].reputation if level_costs[level|string] else level * 5000) %}
                                            <span class="text-muted">声望不足</span>
                                            {% endif %}
                                        {% elif hotel.level >= level %}
                                            <span class="text-muted">已解锁</span>
                                        {% else %}
                                            <span class="text-muted">需先升星</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    function upgradeToLevel(targetLevel) {
        if (confirm('确定要升星到' + targetLevel + '星酒店吗？')) {
            fetch('/hotel/upgrade/' + targetLevel, {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('升星成功');
                    location.reload();
                } else {
                    alert('升星失败: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('升星失败');
            });
        }
    }
</script>
{% endblock %}
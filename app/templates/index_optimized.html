{% extends "base.html" %}

{% block title %}酒店管理系统 - 主页{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 页面标题 -->
    <div class="row">
        <div class="col-12">
            <h1 class="h3 mb-4">
                <i class="bi bi-building"></i> 
                <span id="hotelName">{{ hotel.name }}</span> 
                <small class="text-muted">- 酒店管理系统</small>
            </h1>
        </div>
    </div>

    <!-- 酒店基本信息 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-info-circle"></i> 酒店基本信息
                        <button class="btn btn-sm btn-outline-primary float-end" onclick="editHotelName()">
                            <i class="bi bi-pencil"></i> 修改名称
                        </button>
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-2 mb-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body text-center">
                                    <h6 class="card-title">酒店等级</h6>
                                    <p class="card-text h4" id="hotelLevel">{{ hotel.level }}星</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2 mb-3">
                            <div class="card bg-success text-white">
                                <div class="card-body text-center">
                                    <h6 class="card-title">资金余额</h6>
                                    <p class="card-text h4" id="hotelMoney">¥{{ "{:,}".format(hotel.money) }}</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2 mb-3">
                            <div class="card bg-info text-white">
                                <div class="card-body text-center">
                                    <h6 class="card-title">当前日期</h6>
                                    <p class="card-text h4" id="currentDate">{{ hotel.date.strftime('%Y-%m-%d') }}</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2 mb-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body text-center">
                                    <h6 class="card-title">运营天数</h6>
                                    <p class="card-text h4" id="daysElapsed">{{ hotel.days_elapsed }}天</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2 mb-3">
                            <div class="card bg-secondary text-white">
                                <div class="card-body text-center">
                                    <h6 class="card-title">声望值</h6>
                                    <p class="card-text h4" id="reputation">{{ hotel.reputation }}</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2 mb-3">
                            <div class="card bg-dark text-white">
                                <div class="card-body text-center">
                                    <h6 class="card-title">声望等级</h6>
                                    <p class="card-text h4" id="reputationLevel">{{ hotel.reputation_level }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <div class="card bg-light text-dark">
                                <div class="card-body text-center">
                                    <h6 class="card-title">客户满意度</h6>
                                    <p class="card-text h4" id="satisfaction">{{ "%.1f"|format(hotel.satisfaction) }}分</p>
                                    <div class="progress">
                                        <div class="progress-bar" role="progressbar" style="width: {{ hotel.satisfaction }}%" 
                                             aria-valuenow="{{ hotel.satisfaction }}" aria-valuemin="0" aria-valuemax="100">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="card bg-light text-dark">
                                <div class="card-body">
                                    <h6 class="card-title text-center">时间控制</h6>
                                    <div class="row text-center">
                                        <div class="col-4">
                                            <small>状态</small>
                                            <p class="mb-1" id="timeStatus">{{ '运行中' if hotel.time_running else '已暂停' }}</p>
                                        </div>
                                        <div class="col-4">
                                            <small>速度</small>
                                            <p class="mb-1" id="timeSpeed">{{ hotel.time_speed or 1 }}倍速</p>
                                        </div>
                                        <div class="col-4">
                                            <div class="btn-group-vertical" role="group">
                                                <button id="toggleTimeBtn" class="btn btn-sm btn-outline-warning">暂停/继续</button>
                                                <button id="toggleSpeedBtn" class="btn btn-sm btn-outline-primary">切换速度</button>
                                                <button id="advanceTimeBtn" class="btn btn-sm btn-outline-success">推进一天</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 运营状况概览 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="bi bi-graph-up"></i> 运营状况概览</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <div class="card bg-info text-white">
                                <div class="card-body text-center">
                                    <i class="bi bi-door-open h3"></i>
                                    <h6 class="card-title">房间总数</h6>
                                    <p class="card-text h4" id="totalRooms">0间</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="card bg-success text-white">
                                <div class="card-body text-center">
                                    <i class="bi bi-people h3"></i>
                                    <h6 class="card-title">客户总数</h6>
                                    <p class="card-text h4" id="totalGuests">0人</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body text-center">
                                    <i class="bi bi-person-badge h3"></i>
                                    <h6 class="card-title">员工总数</h6>
                                    <p class="card-text h4" id="totalEmployees">0人</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body text-center">
                                    <i class="bi bi-currency-dollar h3"></i>
                                    <h6 class="card-title">月度利润</h6>
                                    <p class="card-text h4" id="monthlyProfit">¥0</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 游戏管理功能 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="bi bi-gear"></i> 游戏管理</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-2 mb-3">
                            <button id="saveGameBtn" class="btn btn-success w-100">
                                <i class="bi bi-save"></i><br>保存游戏
                            </button>
                        </div>
                        <div class="col-md-2 mb-3">
                            <button id="loadGameBtn" class="btn btn-info w-100">
                                <i class="bi bi-folder-open"></i><br>读取存档
                            </button>
                        </div>
                        <div class="col-md-2 mb-3">
                            <button id="restartGameBtn" class="btn btn-danger w-100">
                                <i class="bi bi-arrow-clockwise"></i><br>重新开始
                            </button>
                        </div>
                        <div class="col-md-2 mb-3">
                            <button id="helpBtn" class="btn btn-secondary w-100">
                                <i class="bi bi-question-circle"></i><br>游戏帮助
                            </button>
                        </div>
                        <div class="col-md-2 mb-3">
                            <button id="exportDataBtn" class="btn btn-outline-primary w-100">
                                <i class="bi bi-download"></i><br>导出数据
                            </button>
                        </div>
                        <div class="col-md-2 mb-3">
                            <button id="settingsBtn" class="btn btn-outline-secondary w-100">
                                <i class="bi bi-sliders"></i><br>游戏设置
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 快捷管理操作 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="bi bi-lightning"></i> 快捷管理</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-2 mb-3">
                            <a href="{{ url_for('departments.management') }}" class="btn btn-primary w-100">
                                <i class="bi bi-diagram-3"></i><br>部门管理
                            </a>
                        </div>
                        <div class="col-md-2 mb-3">
                            <a href="{{ url_for('employees.management') }}" class="btn btn-success w-100">
                                <i class="bi bi-people"></i><br>员工管理
                            </a>
                        </div>
                        <div class="col-md-2 mb-3">
                            <a href="{{ url_for('rooms.management') }}" class="btn btn-info w-100">
                                <i class="bi bi-door-open"></i><br>房间管理
                            </a>
                        </div>
                        <div class="col-md-2 mb-3">
                            <a href="{{ url_for('finance.management') }}" class="btn btn-warning w-100">
                                <i class="bi bi-cash-stack"></i><br>财务管理
                            </a>
                        </div>
                        <div class="col-md-2 mb-3">
                            <a href="{{ url_for('hotel.management') }}" class="btn btn-dark w-100">
                                <i class="bi bi-arrow-up-circle"></i><br>酒店升级
                            </a>
                        </div>
                        <div class="col-md-2 mb-3">
                            <a href="{{ url_for('marketing.management') }}" class="btn btn-outline-primary w-100">
                                <i class="bi bi-megaphone"></i><br>营销管理
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 部门状态与繁忙度 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="bi bi-diagram-3"></i> 部门状态与繁忙度</h5>
                </div>
                <div class="card-body">
                    <div class="row" id="departmentStatusContainer">
                        {% for department in departments %}
                        <div class="col-md-3 mb-3">
                            <div class="card {% if department.is_unlocked %}border-success{% else %}border-secondary{% endif %}">
                                <div class="card-body text-center">
                                    <h6 class="card-title">{{ department.name }}</h6>
                                    {% if department.is_unlocked %}
                                    <span class="badge bg-success mb-2">已解锁</span>
                                    <div class="progress mb-2">
                                        <div class="progress-bar" role="progressbar" style="width: 0%"
                                             id="busy-{{ department.name }}" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">
                                            0%
                                        </div>
                                    </div>
                                    <small class="text-muted">繁忙度</small>
                                    {% else %}
                                    <span class="badge bg-secondary mb-2">未解锁</span>
                                    <p class="small text-muted">解锁费用: ¥{{ "{:,}".format(department.unlock_cost) }}</p>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 入住率数据表格 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="bi bi-graph-up"></i> 最近10天入住率</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped" id="occupancyTable">
                            <thead id="occupancyTableHead">
                                <tr>
                                    <th>房间类型</th>
                                    <!-- 表头将由JavaScript动态填充 -->
                                </tr>
                            </thead>
                            <tbody id="occupancyTableBody">
                                <!-- 表格内容将由JavaScript动态填充 -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

</div>
{% endblock %}

{% block scripts %}
<script>
    // 全局变量
    let isTimeRunning = true;
    let currentTimeSpeed = 1;
    let updateInterval;

    // 页面加载完成后初始化
    document.addEventListener('DOMContentLoaded', function() {
        // 初始化数据
        updateAllData();

        // 设置定时更新（每5秒）
        updateInterval = setInterval(updateAllData, 5000);

        // 绑定所有事件处理器
        bindEventHandlers();
    });

    // 绑定事件处理器
    function bindEventHandlers() {
        // 时间控制按钮
        document.getElementById('toggleTimeBtn').addEventListener('click', toggleTime);
        document.getElementById('toggleSpeedBtn').addEventListener('click', toggleTimeSpeed);
        document.getElementById('advanceTimeBtn').addEventListener('click', advanceTime);

        // 游戏管理按钮
        document.getElementById('saveGameBtn').addEventListener('click', saveGame);
        document.getElementById('loadGameBtn').addEventListener('click', loadGame);
        document.getElementById('restartGameBtn').addEventListener('click', restartGame);
        document.getElementById('helpBtn').addEventListener('click', showHelp);
        document.getElementById('exportDataBtn').addEventListener('click', exportData);
        document.getElementById('settingsBtn').addEventListener('click', showSettings);
    }

    // 更新所有数据
    function updateAllData() {
        fetchHotelInfo();
        fetchOccupancyData();
        fetchDepartmentBusyLevels();
    }

    // 获取酒店基本信息
    function fetchHotelInfo() {
        fetch('/api/hotel_info')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateHotelDisplay(data);
            }
        })
        .catch(error => {
            console.error('获取酒店信息失败:', error);
        });
    }

    // 更新酒店信息显示
    function updateHotelDisplay(data) {
        // 基本信息
        document.getElementById('hotelName').textContent = data.hotel_name;
        document.getElementById('hotelLevel').textContent = data.level + '星';
        document.getElementById('hotelMoney').textContent = '¥' + Number(data.money).toLocaleString();
        document.getElementById('currentDate').textContent = data.current_date;
        document.getElementById('daysElapsed').textContent = data.days_elapsed + '天';
        document.getElementById('reputation').textContent = data.reputation;
        document.getElementById('reputationLevel').textContent = data.reputation_level;

        // 满意度
        if (data.satisfaction !== undefined) {
            document.getElementById('satisfaction').textContent = data.satisfaction.toFixed(1) + '分';
        }

        // 时间状态
        isTimeRunning = data.time_running;
        currentTimeSpeed = data.time_speed || 1;
        document.getElementById('timeStatus').textContent = isTimeRunning ? '运行中' : '已暂停';
        document.getElementById('timeSpeed').textContent = currentTimeSpeed + '倍速';

        // 运营概览
        document.getElementById('totalRooms').textContent = (data.total_rooms || 0) + '间';
        document.getElementById('totalGuests').textContent = Math.round(data.total_guests || 0) + '人';
        document.getElementById('totalEmployees').textContent = (data.employee_count || 0) + '人';
        document.getElementById('monthlyProfit').textContent = '¥' + Number(data.monthly_profit || 0).toLocaleString();
    }

    // 获取入住率数据
    function fetchOccupancyData() {
        fetch('/api/occupancy_data')
        .then(response => response.json())
        .then(data => {
            updateOccupancyTable(data);
        })
        .catch(error => {
            console.error('获取入住率数据失败:', error);
        });
    }

    // 更新入住率表格
    function updateOccupancyTable(occupancyData) {
        const roomTypes = Object.keys(occupancyData);
        if (roomTypes.length === 0) return;

        const thead = document.getElementById('occupancyTableHead').querySelector('tr');
        const tbody = document.getElementById('occupancyTableBody');

        // 清空并重建表头
        thead.innerHTML = '<th>房间类型</th>';

        // 添加日期列
        if (occupancyData[roomTypes[0]] && occupancyData[roomTypes[0]].length > 0) {
            const dates = occupancyData[roomTypes[0]].map(item => item.date);
            dates.forEach(date => {
                const th = document.createElement('th');
                th.textContent = date.split('-')[2] + '日';
                thead.appendChild(th);
            });

            // 重建表格内容
            tbody.innerHTML = '';
            roomTypes.forEach(roomType => {
                const tr = document.createElement('tr');

                // 房间类型列
                const roomTypeTd = document.createElement('td');
                roomTypeTd.textContent = roomType;
                roomTypeTd.className = 'fw-bold';
                tr.appendChild(roomTypeTd);

                // 入住率数据列
                occupancyData[roomType].forEach(item => {
                    const td = document.createElement('td');
                    td.textContent = item.rate + '%';

                    // 根据入住率设置颜色
                    if (item.rate >= 80) {
                        td.className = 'text-success fw-bold';
                    } else if (item.rate >= 50) {
                        td.className = 'text-warning';
                    } else {
                        td.className = 'text-danger';
                    }

                    tr.appendChild(td);
                });

                tbody.appendChild(tr);
            });
        }
    }

    // 获取部门繁忙度
    function fetchDepartmentBusyLevels() {
        // 这里可以添加获取部门繁忙度的API调用
        // 暂时使用模拟数据
    }

    // 时间控制函数
    function toggleTime() {
        fetch('/api/toggle_time', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showMessage(data.message, 'success');
                updateAllData();
            } else {
                showMessage('操作失败: ' + data.message, 'error');
            }
        })
        .catch(error => {
            console.error('切换时间状态失败:', error);
            showMessage('切换时间状态失败', 'error');
        });
    }

    function toggleTimeSpeed() {
        fetch('/api/toggle_time_speed', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showMessage('时间速度已切换为 ' + data.time_speed + '倍速', 'success');
                updateAllData();
            } else {
                showMessage('切换时间速度失败: ' + data.message, 'error');
            }
        })
        .catch(error => {
            console.error('切换时间速度失败:', error);
            showMessage('切换时间速度失败', 'error');
        });
    }

    function advanceTime() {
        fetch('/api/advance_time', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showMessage(data.message, 'success');
                updateAllData();
            } else {
                showMessage('推进时间失败: ' + data.message, 'error');
            }
        })
        .catch(error => {
            console.error('推进时间失败:', error);
            showMessage('推进时间失败', 'error');
        });
    }

    // 游戏管理函数
    function saveGame() {
        const saveName = prompt('请输入存档名称:', '存档_' + new Date().toLocaleString());
        if (saveName) {
            fetch('/api/save_game', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({save_name: saveName})
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showMessage('游戏保存成功: ' + saveName, 'success');
                } else {
                    showMessage('保存失败: ' + data.message, 'error');
                }
            })
            .catch(error => {
                console.error('保存游戏失败:', error);
                showMessage('保存游戏失败', 'error');
            });
        }
    }

    function loadGame() {
        showMessage('读取存档功能开发中...', 'info');
    }

    function restartGame() {
        if (confirm('确定要重新开始游戏吗？当前进度将会丢失！')) {
            fetch('/api/restart_game', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showMessage('游戏已重新开始', 'success');
                    setTimeout(() => location.reload(), 1000);
                } else {
                    showMessage('重新开始失败: ' + data.message, 'error');
                }
            })
            .catch(error => {
                console.error('重新开始游戏失败:', error);
                showMessage('重新开始游戏失败', 'error');
            });
        }
    }

    function showHelp() {
        const helpText = `
酒店管理系统 - 游戏帮助

🎮 基本操作：
• 时间控制：可以暂停/继续时间，切换1倍速/2倍速
• 手动推进：点击"推进一天"手动推进时间

🏨 酒店管理：
• 升级条件：需要满足资金、声望、运营天数、满意度要求
• 部门效果：不同部门解锁后有特殊效果和收入加成
• 满意度：影响声望值变化和客户入住意愿

💰 经营策略：
• 合理招聘员工，避免部门过度繁忙
• 投资营销活动提升入住率
• 平衡收入和支出，确保资金流健康

🏆 成就系统：
• 完成各种成就可获得声望值奖励
• 声望等级影响入住率和随机事件

💾 存档功能：
• 支持保存和读取游戏进度
• 可以导出游戏数据进行备份
        `;
        alert(helpText);
    }

    function exportData() {
        showMessage('数据导出功能开发中...', 'info');
    }

    function showSettings() {
        showMessage('游戏设置功能开发中...', 'info');
    }

    function editHotelName() {
        const newName = prompt('请输入新的酒店名称:', document.getElementById('hotelName').textContent);
        if (newName && newName.trim()) {
            fetch('/api/update_hotel_name', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({name: newName.trim()})
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    document.getElementById('hotelName').textContent = newName.trim();
                    showMessage('酒店名称已更新', 'success');
                } else {
                    showMessage('更新失败: ' + data.message, 'error');
                }
            })
            .catch(error => {
                console.error('更新酒店名称失败:', error);
                showMessage('更新酒店名称失败', 'error');
            });
        }
    }

    // 显示消息提示
    function showMessage(message, type = 'info') {
        // 创建消息提示元素
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type === 'error' ? 'danger' : type === 'success' ? 'success' : 'info'} alert-dismissible fade show position-fixed`;
        alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(alertDiv);

        // 3秒后自动消失
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.parentNode.removeChild(alertDiv);
            }
        }, 3000);
    }
</script>
{% endblock %}

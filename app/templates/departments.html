{% extends "base.html" %}

{% block title %}部门管理 - {{ hotel.name }}{% endblock %}

{% block content %}
<div class="row mb-3">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <h2><i class="bi bi-diagram-3-fill text-primary me-2"></i>部门管理</h2>
            <a href="{{ url_for('main.index') }}" class="btn btn-outline-secondary">
                <i class="bi bi-house-fill me-1"></i>返回首页
            </a>
        </div>
    </div>
</div>

<!-- 部门列表 -->
<div class="card border-0 shadow-sm">
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover align-middle">
                <thead class="table-light">
                    <tr>
                        <th width="5%">状态</th>
                        <th width="20%">部门名称</th>
                        <th width="15%">员工数</th>
                        <th width="15%">繁忙度</th>
                        <th width="20%">解锁条件</th>
                        <th width="15%">解锁费用</th>
                        <th width="10%">操作</th>
                    </tr>
                </thead>
                <tbody>
                    {% for department in all_departments %}
                    <tr class="{% if department.is_unlocked %}table-success{% endif %}">
                        <td>
                            {% if department.is_unlocked %}
                            <i class="bi bi-check-circle-fill text-success fs-5"></i>
                            {% else %}
                            <i class="bi bi-lock-fill text-muted fs-5"></i>
                            {% endif %}
                        </td>
                        <td>
                            <div class="d-flex align-items-center">
                                <strong>{{ department.name }}</strong>
                                {% if department.is_unlocked %}
                                <span class="badge bg-success ms-2">已解锁</span>
                                {% else %}
                                <span class="badge bg-secondary ms-2">未解锁</span>
                                {% endif %}
                            </div>
                        </td>
                        <td>
                            {% if department.is_unlocked %}
                            <span class="badge bg-primary">{{ department_employee_counts.get(department.name, 0) }}人</span>
                            {% else %}
                            <span class="text-muted">-</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if department.is_unlocked %}
                            <div class="d-flex align-items-center">
                                <div class="progress me-2" style="width: 80px; height: 8px;">
                                    <div class="progress-bar bg-info" style="width: {{ department_busy_levels.get(department.name, 0) }}%"></div>
                                </div>
                                <small>{{ "%.1f"|format(department_busy_levels.get(department.name, 0)) }}%</small>
                            </div>
                            {% else %}
                            <span class="text-muted">-</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if not department.is_unlocked %}
                            <small class="text-muted">需要{{ department.required_level }}星酒店</small>
                            {% else %}
                            <span class="text-success">✓ 已满足</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if not department.is_unlocked %}
                            <span class="text-warning">¥{{ "{:,}".format(department.unlock_cost) }}</span>
                            {% else %}
                            <span class="text-success">已支付</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if department.is_unlocked %}
                            <a href="{{ url_for('departments.department_detail', department_id=department.id) }}"
                               class="btn btn-sm btn-primary">
                                <i class="bi bi-eye-fill"></i>
                            </a>
                            {% elif department.can_unlock %}
                            <button class="btn btn-sm btn-warning" onclick="unlockDepartment({{ department.id }})">
                                <i class="bi bi-unlock-fill"></i>
                            </button>
                            {% else %}
                            <button class="btn btn-sm btn-secondary" disabled>
                                <i class="bi bi-lock-fill"></i>
                            </button>
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function unlockDepartment(departmentId) {
    if (confirm('确定要解锁这个部门吗？')) {
        apiRequest(`/departments/unlock/${departmentId}`, {
            method: 'POST'
        }).then(data => {
            if (data.success) {
                showMessage(data.message, 'success');
                setTimeout(() => location.reload(), 1000);
            } else {
                showMessage(data.message, 'error');
            }
        }).catch(error => {
            showMessage('解锁部门失败', 'error');
        });
    }
}
</script>
{% endblock %}

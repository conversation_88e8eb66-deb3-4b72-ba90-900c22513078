{% extends "base.html" %}

{% block title %}部门管理 - {{ hotel.name }}{% endblock %}

{% block content %}
<div class="row mb-3">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <h2><i class="bi bi-diagram-3-fill text-primary me-2"></i>部门管理</h2>
            <a href="{{ url_for('main.index') }}" class="btn btn-outline-secondary">
                <i class="bi bi-house-fill me-1"></i>返回首页
            </a>
        </div>
    </div>
</div>

<div class="row">
    {% for department in all_departments %}
    <div class="col-lg-4 col-md-6 mb-4">
        <div class="card h-100 {% if department.is_unlocked %}border-success{% else %}border-secondary{% endif %}">
            <div class="card-header {% if department.is_unlocked %}bg-success text-white{% else %}bg-secondary text-white{% endif %}">
                <h5 class="mb-0">
                    {% if department.is_unlocked %}
                    <i class="bi bi-check-circle-fill me-2"></i>
                    {% else %}
                    <i class="bi bi-lock-fill me-2"></i>
                    {% endif %}
                    {{ department.name }}
                </h5>
            </div>
            <div class="card-body">
                {% if department.is_unlocked %}
                    <div class="mb-3">
                        <h6>部门状态</h6>
                        <span class="badge bg-success">已解锁</span>
                        <span class="badge bg-info ms-2">{{ department_employee_counts.get(department.name, 0) }}名员工</span>
                    </div>
                    
                    <div class="mb-3">
                        <h6>繁忙度</h6>
                        <div class="progress">
                            <div class="progress-bar" role="progressbar" 
                                 style="width: {{ department_busy_levels.get(department.name, 0) }}%">
                                {{ "%.1f"|format(department_busy_levels.get(department.name, 0)) }}%
                            </div>
                        </div>
                    </div>
                    
                    <div class="d-grid">
                        <a href="{{ url_for('departments.department_detail', department_id=department.id) }}" 
                           class="btn btn-primary">
                            <i class="bi bi-eye-fill me-1"></i>查看详情
                        </a>
                    </div>
                {% else %}
                    <div class="mb-3">
                        <h6>解锁条件</h6>
                        <p class="text-muted mb-1">需要酒店等级：{{ department.required_level }}星</p>
                        <p class="text-muted mb-1">解锁费用：¥{{ "{:,}".format(department.unlock_cost) }}</p>
                        <p class="text-muted">当前酒店等级：{{ hotel.level }}星</p>
                    </div>
                    
                    {% if department.can_unlock %}
                    <div class="d-grid">
                        <button class="btn btn-warning" onclick="unlockDepartment({{ department.id }})">
                            <i class="bi bi-unlock-fill me-1"></i>解锁部门
                        </button>
                    </div>
                    {% else %}
                    <div class="d-grid">
                        <button class="btn btn-secondary" disabled>
                            <i class="bi bi-lock-fill me-1"></i>{{ department.unlock_condition }}
                        </button>
                    </div>
                    {% endif %}
                {% endif %}
            </div>
        </div>
    </div>
    {% endfor %}
</div>
{% endblock %}

{% block scripts %}
<script>
function unlockDepartment(departmentId) {
    if (confirm('确定要解锁这个部门吗？')) {
        apiRequest(`/departments/unlock/${departmentId}`, {
            method: 'POST'
        }).then(data => {
            if (data.success) {
                showMessage(data.message, 'success');
                setTimeout(() => location.reload(), 1000);
            } else {
                showMessage(data.message, 'error');
            }
        }).catch(error => {
            showMessage('解锁部门失败', 'error');
        });
    }
}
</script>
{% endblock %}

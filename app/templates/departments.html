{% extends "base.html" %}

{% block title %}部门管理 - 酒店管理系统{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">部门管理</h1>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">部门列表</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th>部门名称</th>
                                    <th>解锁条件</th>
                                    <th>解锁费用</th>
                                    <th>员工人数</th>
                                    <th>繁忙度(月度均值)</th>
                                    <th>状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for department in departments %}
                                <tr>
                                    <td>{{ department.name }}</td>
                                    <td>{{ department.unlock_condition }}星酒店</td>
                                    <td>¥{{ department.unlock_cost }}</td>
                                    <td>{{ department.employee_count }}人</td>
                                    <td>
                                        {% set busy_level = department_busy_levels.get(department.name, 0) %}
                                        <div class="progress" style="height: 20px;">
                                            <div class="progress-bar 
                                                {% if busy_level > 100 %}bg-danger
                                                {% elif busy_level > 80 %}bg-warning
                                                {% elif busy_level > 60 %}bg-info
                                                {% else %}bg-success{% endif %}" 
                                                role="progressbar" 
                                                style="width: {{ [busy_level, 100]|min }}%" 
                                                aria-valuenow="{{ busy_level|round(1) }}" 
                                                aria-valuemin="0" 
                                                aria-valuemax="100">
                                                {{ busy_level|round(1) }}%
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        {% if department.is_unlocked %}
                                            <span class="badge badge-success">已解锁</span>
                                        {% else %}
                                            <span class="badge badge-secondary">未解锁</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if department.is_unlocked %}
                                            <a href="{{ url_for('departments.department_detail', department_id=department.id) }}" 
                                               class="btn btn-primary btn-sm">管理</a>
                                        {% else %}
                                            <button type="button" 
                                                    class="btn btn-success btn-sm unlock-department" 
                                                    data-department-id="{{ department.id }}">解锁</button>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 解锁部门确认模态框 -->
<div class="modal fade" id="unlockDepartmentModal" tabindex="-1" aria-labelledby="unlockDepartmentModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="unlockDepartmentModalLabel">解锁部门</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                确定要解锁 <span id="departmentName"></span> 吗？这将花费 ¥<span id="unlockCost"></span>。
                <input type="hidden" id="departmentIdInput">
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="confirmUnlock">确认解锁</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        let currentDepartmentId = 0;
        
        // 检查 jQuery 是否加载
        if (typeof $ !== 'undefined') {
            $('.unlock-department').click(function() {
                const departmentId = $(this).data('department-id');
                const departmentName = $(this).closest('tr').find('td:first').text();
                const unlockCostText = $(this).closest('tr').find('td:nth-child(3)').text().replace('¥', '').replace(/,/g, '');
                const unlockCost = parseInt(unlockCostText) || 0;
                
                currentDepartmentId = departmentId;
                document.getElementById('departmentName').textContent = departmentName;
                document.getElementById('unlockCost').textContent = unlockCost.toLocaleString();
                var unlockModal = new bootstrap.Modal(document.getElementById('unlockDepartmentModal'));
                unlockModal.show();
            });
        }
        
        // 确认解锁按钮事件监听
        const confirmUnlockBtn = document.getElementById('confirmUnlock');
        if (confirmUnlockBtn) {
            confirmUnlockBtn.addEventListener('click', function() {
                if (currentDepartmentId === 0) {
                    alert('请选择要解锁的部门');
                    return;
                }
                
                fetch('/departments/unlock/' + currentDepartmentId, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        location.reload();
                    } else {
                        alert('解锁失败: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('解锁过程中发生错误');
                });
            });
        }
    });
</script>
{% endblock %}
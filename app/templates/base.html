<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}酒店管理系统{% endblock %}</title>
    
    <!-- Bootstrap 5 CSS - 多重备用方案 -->
    <link href="https://cdn.bootcdn.net/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css" rel="stylesheet"
          onerror="this.onerror=null;this.href='https://unpkg.com/bootstrap@5.3.0/dist/css/bootstrap.min.css'">
    <!-- Bootstrap Icons - 多重备用方案 -->
    <link href="https://cdn.bootcdn.net/ajax/libs/bootstrap-icons/1.10.0/font/bootstrap-icons.css" rel="stylesheet"
          onerror="this.onerror=null;this.href='https://unpkg.com/bootstrap-icons@1.10.0/font/bootstrap-icons.css'">

    <!-- 基础样式备用方案 -->
    <style>
        /* 如果Bootstrap加载失败的基础样式 */
        .container { max-width: 1200px; margin: 0 auto; padding: 0 15px; }
        .row { display: flex; flex-wrap: wrap; margin: 0 -15px; }
        .col, .col-6, .col-md-6, .col-lg-4, .col-lg-8, .col-xl-3 { padding: 0 15px; flex: 1; }
        .col-6 { flex: 0 0 50%; }
        .col-md-6 { flex: 0 0 50%; }
        .col-lg-4 { flex: 0 0 33.333333%; }
        .col-lg-8 { flex: 0 0 66.666667%; }
        .col-xl-3 { flex: 0 0 25%; }
        .card { border: 1px solid #dee2e6; border-radius: 8px; margin-bottom: 1rem; background: white; box-shadow: 0 0.125rem 0.25rem rgba(0,0,0,0.075); }
        .card-body { padding: 1rem; }
        .card-header { padding: 0.75rem 1rem; background-color: #f8f9fa; border-bottom: 1px solid #dee2e6; }
        .btn { display: inline-block; padding: 0.375rem 0.75rem; margin-bottom: 0; font-size: 1rem; line-height: 1.5; text-align: center; text-decoration: none; vertical-align: middle; cursor: pointer; border: 1px solid transparent; border-radius: 0.375rem; }
        .btn-outline-primary { color: #0d6efd; border-color: #0d6efd; }
        .btn-outline-success { color: #198754; border-color: #198754; }
        .btn-outline-warning { color: #ffc107; border-color: #ffc107; }
        .btn-outline-danger { color: #dc3545; border-color: #dc3545; }
        .btn-outline-info { color: #0dcaf0; border-color: #0dcaf0; }
        .btn-outline-secondary { color: #6c757d; border-color: #6c757d; }
        .badge { display: inline-block; padding: 0.35em 0.65em; font-size: 0.75em; font-weight: 700; line-height: 1; color: #fff; text-align: center; white-space: nowrap; vertical-align: baseline; border-radius: 0.375rem; }
        .bg-success { background-color: #198754 !important; }
        .bg-primary { background-color: #0d6efd !important; }
        .bg-warning { background-color: #ffc107 !important; }
        .bg-light { background-color: #f8f9fa !important; }
        .text-muted { color: #6c757d !important; }
        .mb-0 { margin-bottom: 0 !important; }
        .mb-1 { margin-bottom: 0.25rem !important; }
        .mb-2 { margin-bottom: 0.5rem !important; }
        .mb-3 { margin-bottom: 1rem !important; }
        .me-1 { margin-right: 0.25rem !important; }
        .me-2 { margin-right: 0.5rem !important; }
        .p-1 { padding: 0.25rem !important; }
        .p-2 { padding: 0.5rem !important; }
        .rounded { border-radius: 0.375rem !important; }
        .text-center { text-align: center !important; }
        .d-flex { display: flex !important; }
        .align-items-center { align-items: center !important; }
        .justify-content-between { justify-content: space-between !important; }
        .w-100 { width: 100% !important; }
        .shadow-sm { box-shadow: 0 0.125rem 0.25rem rgba(0,0,0,0.075) !important; }
    </style>
    
    <!-- 自定义样式 -->
    <style>
        :root {
            --primary-color: #0d6efd;
            --success-color: #198754;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
            --info-color: #0dcaf0;
            --dark-color: #212529;
        }
        
        body {
            font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
        }
        
        .card {
            border: none;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            border-radius: 0.5rem;
        }
        
        .card-header {
            background-color: var(--dark-color);
            color: white;
            border-radius: 0.5rem 0.5rem 0 0 !important;
            padding: 0.75rem 1rem;
        }
        
        .btn {
            border-radius: 0.375rem;
            font-weight: 500;
        }
        
        .progress {
            height: 0.5rem;
            border-radius: 0.25rem;
        }
        
        .badge {
            font-size: 0.75em;
            font-weight: 500;
        }
        
        .table {
            margin-bottom: 0;
        }
        
        .table th {
            border-top: none;
            font-weight: 600;
            font-size: 0.875rem;
        }
        
        .table td {
            font-size: 0.875rem;
            vertical-align: middle;
        }
        
        /* 消息提示样式 */
        .alert {
            border: none;
            border-radius: 0.5rem;
            font-weight: 500;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .container-fluid {
                padding-left: 0.5rem;
                padding-right: 0.5rem;
            }
            
            .card-body {
                padding: 0.75rem;
            }
            
            .btn {
                font-size: 0.875rem;
            }
        }
        
        /* 动画效果 */
        .fade-in {
            animation: fadeIn 0.3s ease-in;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        /* 悬停效果 */
        .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.15);
        }
        
        .card:hover {
            box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
        }
    </style>
    
    {% block styles %}{% endblock %}
</head>
<body>
    <!-- 消息提示容器 -->
    <div id="messageContainer" class="position-fixed top-0 end-0 p-3" style="z-index: 9999;"></div>
    
    <!-- 主要内容 -->
    <main class="container-fluid p-1">
        {% block content %}{% endblock %}
    </main>
    
    <!-- Bootstrap 5 JS - 多重备用方案 -->
    <script src="https://cdn.bootcdn.net/ajax/libs/bootstrap/5.3.0/js/bootstrap.bundle.min.js"
            onerror="document.write('<script src=\'https://unpkg.com/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js\'><\/script>')"></script>
    <!-- Chart.js - 多重备用方案 -->
    <script src="https://cdn.bootcdn.net/ajax/libs/Chart.js/3.9.1/chart.min.js"
            onerror="document.write('<script src=\'https://unpkg.com/chart.js@3.9.1/dist/chart.min.js\'><\/script>')"></script>
    
    <!-- 通用JavaScript功能 -->
    <script>
        // 全局消息提示函数
        function showMessage(message, type = 'info', duration = 3000) {
            const container = document.getElementById('messageContainer');
            if (!container) return;
            
            const alertDiv = document.createElement('div');
            const alertClass = type === 'error' ? 'danger' : type;
            const icon = type === 'success' ? 'check-circle-fill' : 
                        type === 'error' ? 'exclamation-triangle-fill' : 
                        'info-circle-fill';
            
            alertDiv.className = `alert alert-${alertClass} alert-dismissible fade show fade-in`;
            alertDiv.innerHTML = `
                <i class="bi bi-${icon} me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            
            container.appendChild(alertDiv);
            
            // 自动消失
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.remove();
                }
            }, duration);
        }
        
        // 全局确认对话框
        function confirmAction(message, callback) {
            if (confirm(message)) {
                callback();
            }
        }
        
        // 全局API请求函数
        async function apiRequest(url, options = {}) {
            try {
                const response = await fetch(url, {
                    headers: {
                        'Content-Type': 'application/json',
                        ...options.headers
                    },
                    ...options
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}`);
                }
                
                return await response.json();
            } catch (error) {
                console.error('API请求失败:', error);
                showMessage('网络请求失败，请检查连接', 'error');
                throw error;
            }
        }
        
        // 格式化数字
        function formatNumber(num) {
            return new Intl.NumberFormat('zh-CN').format(num);
        }
        
        // 格式化货币
        function formatCurrency(amount) {
            return '¥' + formatNumber(amount);
        }
        
        // 更新元素内容
        function updateElement(id, content) {
            const element = document.getElementById(id);
            if (element) {
                element.textContent = content;
            }
        }
        
        // 页面加载完成后的通用初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('页面加载完成');
            
            // 添加所有按钮的悬停效果
            document.querySelectorAll('.btn').forEach(btn => {
                btn.addEventListener('mouseenter', function() {
                    this.style.transition = 'all 0.2s ease';
                });
            });
            
            // 添加所有卡片的悬停效果
            document.querySelectorAll('.card').forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transition = 'all 0.2s ease';
                });
            });
        });
    </script>
    
    {% block scripts %}{% endblock %}
</body>
</html>

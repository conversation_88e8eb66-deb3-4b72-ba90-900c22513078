{% extends "base.html" %}

{% block title %}房间管理 - 酒店管理系统{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">房间管理</h1>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">房间列表</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover">
                            <thead class="thead-dark">
                                <tr>
                                    <th>房间类型</th>
                                    <th>状态</th>
                                    <th>价格</th>
                                    <th>数量</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for room_type, room_info in room_info_dict.items() %}
                                <tr>
                                    <td>{{ room_type }}</td>
                                    <td>
                                        {% if room_info.unlocked %}
                                            <span class="badge bg-success">已解锁</span>
                                        {% else %}
                                            <span class="badge bg-secondary">未解锁</span>
                                        {% endif %}
                                    </td>
                                    <td>¥{{ "{:,}".format(room_info.price) }}</td>
                                    <td>{{ room_info.count }}</td>
                                    <td>
                                        {% if room_info.unlocked %}
                                            <button class="btn btn-sm btn-primary" onclick="showAddRoomModal('{{ room_type }}', {{ room_info.price }})">
                                                <i class="bi bi-plus-circle"></i> 添加房间
                                            </button>
                                        {% else %}
                                            {% if room_info.can_unlock %}
                                                <button class="btn btn-sm btn-success" onclick="unlockRoomType('{{ room_type }}', {{ room_info.unlock_cost }})">
                                                    <i class="bi bi-unlock"></i> 解锁 (¥{{ "{:,}".format(room_info.unlock_cost) }})
                                                </button>
                                            {% else %}
                                                <span class="text-muted">酒店等级不足</span>
                                            {% endif %}
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 添加房间模态框 -->
<div class="modal fade" id="addRoomModal" tabindex="-1" aria-labelledby="addRoomModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addRoomModalLabel">添加房间</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="addRoomForm">
                    <input type="hidden" id="roomTypeInput">
                    <div class="mb-3">
                        <label for="roomTypeDisplay" class="form-label">房间类型</label>
                        <input type="text" class="form-control" id="roomTypeDisplay" readonly>
                    </div>
                    <div class="mb-3">
                        <label for="roomCountInput" class="form-label">添加数量</label>
                        <input type="number" class="form-control" id="roomCountInput" name="count" min="1" value="1">
                    </div>
                    <div class="mb-3">
                        <label for="roomPriceDisplay" class="form-label">房间单价</label>
                        <input type="text" class="form-control" id="roomPriceDisplay" readonly>
                    </div>
                    <div class="mb-3">
                        <label for="roomCostDisplay" class="form-label">预计费用</label>
                        <input type="text" class="form-control" id="roomCostDisplay" readonly>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="addRooms()">确认添加</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // 房间成本映射 (根据需求文档更新)
    const roomCosts = {
        "单人间": 200,
        "标准间": 500,
        "大床房": 800,
        "家庭房": 1200,
        "商务间": 2000,
        "行政间": 3000,
        "豪华间": 5000,
        "总统套房": 8000
    };

    // 显示添加房间模态框
    function showAddRoomModal(roomType, roomPrice) {
        document.getElementById('roomTypeInput').value = roomType;
        document.getElementById('roomTypeDisplay').value = roomType;
        document.getElementById('roomPriceDisplay').value = '¥' + roomPrice.toLocaleString();
        
        // 初始化数量为1
        document.getElementById('roomCountInput').value = 1;
        
        // 更新费用显示
        updateRoomCost();
        
        // 显示模态框
        var addRoomModal = new bootstrap.Modal(document.getElementById('addRoomModal'));
        addRoomModal.show();
    }

    // 更新房间费用显示
    function updateRoomCost() {
        const roomType = document.getElementById('roomTypeInput').value;
        const count = parseInt(document.getElementById('roomCountInput').value) || 0;
        const cost = roomCosts[roomType] || 0;
        const totalCost = cost * count;
        
        document.getElementById('roomCostDisplay').value = '¥' + totalCost.toLocaleString();
    }

    // 添加房间数量变化监听器
    document.addEventListener('DOMContentLoaded', function() {
        const roomCountInput = document.getElementById('roomCountInput');
        if (roomCountInput) {
            roomCountInput.addEventListener('input', updateRoomCost);
        }
    });
    
    // 添加房间
    function addRooms() {
        const roomType = document.getElementById('roomTypeInput').value;
        const count = parseInt(document.getElementById('roomCountInput').value);
        
        if (!roomType) {
            alert('请选择房间类型');
            return;
        }
        
        if (isNaN(count) || count <= 0) {
            alert('请输入有效的房间数量');
            return;
        }

        fetch('/rooms/add_room', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({room_type: roomType, count: count})
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('添加成功');
                // 关闭模态框
                var addRoomModal = bootstrap.Modal.getInstance(document.getElementById('addRoomModal'));
                addRoomModal.hide();
                // 刷新页面
                location.reload();
            } else {
                alert('添加失败: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('添加失败');
        });
    }

    // 解锁房间类型
    function unlockRoomType(roomType, cost) {
        if (confirm(`确定要花费 ¥${cost.toLocaleString()} 解锁 ${roomType} 吗？`)) {
            fetch('/rooms/unlock', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({room_type: roomType})
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('解锁成功');
                    location.reload();
                } else {
                    alert('解锁失败: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('解锁失败');
            });
        }
    }
</script>
{% endblock %}
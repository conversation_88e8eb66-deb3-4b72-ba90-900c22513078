{% extends "base.html" %}

{% block title %}房间管理 - {{ hotel.name }}{% endblock %}

{% block content %}
<div class="row mb-3">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <h2><i class="bi bi-door-open-fill text-info me-2"></i>房间管理</h2>
            <a href="{{ url_for('main.index') }}" class="btn btn-outline-secondary">
                <i class="bi bi-house-fill me-1"></i>返回首页
            </a>
        </div>
    </div>
</div>

<!-- 房间概况 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <h5 class="card-title mb-3">
                    <i class="bi bi-graph-up text-primary me-2"></i>房间概况
                </h5>
                <div class="row text-center">
                    <div class="col-md-3">
                        <div class="bg-primary bg-opacity-10 rounded p-3">
                            <h4 class="text-primary mb-1">{{ total_rooms }}</h4>
                            <small class="text-muted">房间总数</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="bg-success bg-opacity-10 rounded p-3">
                            <h4 class="text-success mb-1">{{ occupied_rooms }}</h4>
                            <small class="text-muted">已入住</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="bg-warning bg-opacity-10 rounded p-3">
                            <h4 class="text-warning mb-1">{{ "%.1f"|format(occupancy_rate) }}%</h4>
                            <small class="text-muted">入住率</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="bg-info bg-opacity-10 rounded p-3">
                            <h4 class="text-info mb-1">¥{{ "{:,}".format(monthly_maintenance) }}</h4>
                            <small class="text-muted">月维护费</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 房间类型管理 -->
<div class="row">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5 class="mb-0">
                        <i class="bi bi-building text-primary me-2"></i>房间类型管理
                    </h5>
                    <button class="btn btn-primary btn-sm" onclick="showBuildModal()">
                        <i class="bi bi-plus-circle me-1"></i>建设房间
                    </button>
                </div>

                <div class="table-responsive">
                    <table class="table table-hover align-middle">
                        <thead class="table-light">
                            <tr>
                                <th width="20%">房间类型</th>
                                <th width="12%">数量</th>
                                <th width="15%">单价</th>
                                <th width="15%">建设成本</th>
                                <th width="15%">入住率</th>
                                <th width="13%">状态</th>
                                <th width="10%">操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for room_type, rooms in rooms_by_type.items() %}
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <i class="bi bi-house-door text-primary me-2"></i>
                                        <strong>{{ room_type }}</strong>
                                    </div>
                                </td>
                                <td>
                                    {% if rooms %}
                                    <span class="badge bg-primary fs-6">{{ rooms|length }}间</span>
                                    {% else %}
                                    <span class="text-muted">0间</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <span class="text-success fw-bold">¥{{ "{:,}".format(room_prices[room_type]) }}/晚</span>
                                </td>
                                <td>
                                    <span class="text-warning">¥{{ "{:,}".format(room_prices[room_type] * 10) }}</span>
                                </td>
                                <td>
                                    {% if rooms %}
                                    <div class="d-flex align-items-center">
                                        <div class="progress me-2" style="width: 80px; height: 8px;">
                                            <div class="progress-bar bg-success" style="width: {{ room_occupancy_rates.get(room_type, 0) }}%"></div>
                                        </div>
                                        <small>{{ "%.1f"|format(room_occupancy_rates.get(room_type, 0)) }}%</small>
                                    </div>
                                    {% else %}
                                    <span class="text-muted">-</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if rooms %}
                                    <span class="badge bg-success">运营中</span>
                                    {% elif hotel.level >= room_requirements.get(room_type, 1) %}
                                    <span class="badge bg-warning">可建设</span>
                                    {% else %}
                                    <span class="badge bg-secondary">需{{ room_requirements.get(room_type, 1) }}星</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if rooms %}
                                    <button class="btn btn-sm btn-primary" onclick="buildRoom('{{ room_type }}')">
                                        <i class="bi bi-plus"></i>
                                    </button>
                                    {% elif hotel.level >= room_requirements.get(room_type, 1) %}
                                    <button class="btn btn-sm btn-success" onclick="buildRoom('{{ room_type }}')">
                                        <i class="bi bi-hammer"></i>
                                    </button>
                                    {% else %}
                                    <button class="btn btn-sm btn-secondary" disabled>
                                        <i class="bi bi-lock"></i>
                                    </button>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function showBuildModal() {
    showMessage('批量建设功能开发中...', 'info');
}

function buildRoom(roomType) {
    const quantity = prompt(`请输入要建设的${roomType}数量:`, '1');
    if (quantity && parseInt(quantity) > 0) {
        apiRequest('/rooms/build', {
            method: 'POST',
            body: JSON.stringify({
                room_type: roomType,
                quantity: parseInt(quantity)
            })
        }).then(data => {
            if (data.success) {
                showMessage(data.message, 'success');
                setTimeout(() => location.reload(), 1000);
            } else {
                showMessage(data.message, 'error');
            }
        }).catch(error => {
            showMessage('建设房间失败', 'error');
        });
    }
}
</script>
{% endblock %}

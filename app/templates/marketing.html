{% extends "base.html" %}

{% block title %}营销管理 - 酒店管理系统{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">营销管理</h1>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">广告投放</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover">
                            <thead class="thead-dark">
                                <tr>
                                    <th>广告类型</th>
                                    <th>效果</th>
                                    <th>持续时间</th>
                                    <th>花费</th>
                                    <th>酒店等级要求</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for rule in marketing_rules.values() %}
                                <tr class="{% if hotel.level < rule.min_hotel_level %}table-secondary{% endif %}">
                                    <td>{{ rule.display_name }}</td>
                                    <td>{{ rule.effect }}</td>
                                    <td>{{ rule.duration }}</td>
                                    <td>¥{{ "{:,}".format(rule.cost) }}</td>
                                    <td>{{ rule.min_hotel_level }}级</td>
                                    <td>
                                        {% if hotel.level >= rule.min_hotel_level %}
                                            {% if hotel.money >= rule.cost %}
                                            <button class="btn btn-sm btn-primary" onclick="startActivity({{ rule.level }})">
                                                <i class="bi bi-play-circle"></i> 投放广告
                                            </button>
                                            {% else %}
                                            <span class="text-muted">资金不足</span>
                                            {% endif %}
                                        {% else %}
                                            <span class="text-muted">等级不足</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    function startActivity(level) {
        // 根据等级获取活动信息
        const activityRules = {
            1: {"cost": 50000, "effect": "提升入住率5%", "duration": "7天"},
            2: {"cost": 200000, "effect": "提升入住率10%", "duration": "15天"},
            3: {"cost": 500000, "effect": "提升入住率15%", "duration": "30天"},
            4: {"cost": 1000000, "effect": "提升入住率20%", "duration": "30天"}
        };
        
        const rule = activityRules[level];
        if (!rule) {
            alert('无效的活动等级');
            return;
        }
        
        if (confirm(`确定要投放${rule.effect}的${rule.duration}广告吗？\n\n花费: ¥${rule.cost.toLocaleString()}`)) {
            fetch('/marketing/start_activity', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    level: level,
                    cost: rule.cost,
                    effect: rule.effect,
                    duration: rule.duration
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert(data.message);
                    location.reload();
                } else {
                    alert('广告投放失败: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('广告投放失败');
            });
        }
    }
</script>
{% endblock %}
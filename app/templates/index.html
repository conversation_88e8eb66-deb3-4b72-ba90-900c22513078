{% extends "base.html" %}

{% block title %}{{ hotel.name }} - 酒店管理系统{% endblock %}

{% block content %}
<!-- 顶部导航栏 - 参考Hotel Giant风格 -->
<div class="row mb-2">
    <div class="col-12">
        <div class="card border-0 shadow-lg" style="background: linear-gradient(135deg, #2c3e50 0%, #3498db 50%, #9b59b6 100%); border-radius: 15px;">
            <div class="card-body py-2 px-4">
                <div class="d-flex justify-content-between align-items-center">
                    <div class="d-flex align-items-center">
                        <div class="bg-white bg-opacity-20 rounded-circle p-2 me-3">
                            <i class="bi bi-building-fill text-white" style="font-size: 1.2rem;"></i>
                        </div>
                        <div>
                            <h1 class="h5 mb-0 text-white fw-bold">
                                <span id="hotelName" style="cursor: pointer;" onclick="editHotelName()">{{ hotel.name or '红珊瑚大酒店' }}</span>
                            </h1>
                            <small class="text-white-50">Premium Hotel Management</small>
                        </div>
                    </div>
                    <div class="text-end">
                        <div class="bg-white bg-opacity-20 rounded px-3 py-1">
                            <div class="text-white fw-bold" id="currentDate" style="font-size: 1rem;">{{ hotel.date.strftime('%Y-%m-%d') if hotel.date else '1990-01-01' }}</div>
                            <small class="text-white-50">第 <span id="daysElapsed">{{ hotel.days_elapsed or 0 }}</span> 天</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 核心数据仪表盘 - 参考Two Point Hospital风格 -->
<div class="row mb-2">
    <div class="col-lg-3 col-md-6 mb-2">
        <div class="card border-0 shadow-sm h-100" style="background: linear-gradient(135deg, #ffeaa7 0%, #fdcb6e 100%); border-radius: 12px;">
            <div class="card-body py-2 px-3 text-center">
                <div class="d-flex flex-column align-items-center">
                    <div class="bg-white bg-opacity-30 rounded-circle p-2 mb-1">
                        <i class="bi bi-star-fill text-white" style="font-size: 1.1rem;"></i>
                    </div>
                    <div class="text-white fw-bold" id="hotelLevel" style="font-size: 1.3rem;">{{ hotel.level }}★</div>
                    <small class="text-white-50" style="font-size: 0.7rem;">酒店等级</small>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-2">
        <div class="card border-0 shadow-sm h-100" style="background: linear-gradient(135deg, #55efc4 0%, #00b894 100%); border-radius: 12px;">
            <div class="card-body py-2 px-3 text-center">
                <div class="d-flex flex-column align-items-center">
                    <div class="bg-white bg-opacity-30 rounded-circle p-2 mb-1">
                        <i class="bi bi-cash-stack text-white" style="font-size: 1.1rem;"></i>
                    </div>
                    <div class="text-white fw-bold" id="hotelMoney" style="font-size: 1.1rem;">¥{{ "{:,}".format(hotel.money) }}</div>
                    <small class="text-white-50" style="font-size: 0.7rem;">资金余额</small>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-2">
        <div class="card border-0 shadow-sm h-100" style="background: linear-gradient(135deg, #fd79a8 0%, #e84393 100%); border-radius: 12px;">
            <div class="card-body py-2 px-3 text-center">
                <div class="d-flex flex-column align-items-center">
                    <div class="bg-white bg-opacity-30 rounded-circle p-2 mb-1">
                        <i class="bi bi-heart-fill text-white" style="font-size: 1.1rem;"></i>
                    </div>
                    <div class="text-white fw-bold" id="satisfaction" style="font-size: 1.1rem;">{{ "%.0f"|format(hotel.satisfaction) }}%</div>
                    <small class="text-white-50" style="font-size: 0.7rem;">客户满意度</small>
                    <div class="progress mt-1 bg-white bg-opacity-30" style="height: 3px; border-radius: 3px;">
                        <div class="progress-bar bg-white" role="progressbar"
                             style="width: {{ hotel.satisfaction }}%; border-radius: 3px;" id="satisfactionBar">
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-2">
        <div class="card border-0 shadow-sm h-100" style="background: linear-gradient(135deg, #a29bfe 0%, #6c5ce7 100%); border-radius: 12px;">
            <div class="card-body py-2 px-3 text-center">
                <div class="d-flex flex-column align-items-center">
                    <div class="bg-white bg-opacity-30 rounded-circle p-2 mb-1">
                        <i class="bi bi-trophy-fill text-white" style="font-size: 1.1rem;"></i>
                    </div>
                    <div class="text-white fw-bold" id="reputation" style="font-size: 1.1rem;">{{ hotel.reputation }}</div>
                    <small class="text-white-50" style="font-size: 0.7rem;">声望值</small>
                    <div class="mt-1"><span class="badge bg-white text-dark" style="font-size: 0.6rem;" id="reputationLevel">{{ hotel.reputation_level }}</span></div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 主要内容区域 -->
<div class="row mb-4">
    <!-- 左侧：数据展示区域 -->
    <div class="col-lg-8">
        <!-- 运营数据面板 - 参考RollerCoaster Tycoon风格 -->
        <div class="card border-0 shadow-sm mb-2" style="border-radius: 10px;">
            <div class="card-header bg-gradient text-white py-2" style="background: linear-gradient(90deg, #74b9ff 0%, #0984e3 100%); border-radius: 10px 10px 0 0;">
                <h6 class="mb-0 fw-bold" style="font-size: 0.85rem;">
                    <i class="bi bi-graph-up me-2"></i>运营数据总览
                </h6>
            </div>
            <div class="card-body py-2">
                <div class="row g-2">
                    <div class="col-6">
                        <div class="d-flex align-items-center justify-content-between p-2 bg-light rounded">
                            <div class="d-flex align-items-center">
                                <i class="bi bi-door-open-fill text-primary me-2"></i>
                                <small class="text-muted">房间</small>
                            </div>
                            <span class="fw-bold text-primary" id="totalRooms">0</span>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="d-flex align-items-center justify-content-between p-2 bg-light rounded">
                            <div class="d-flex align-items-center">
                                <i class="bi bi-people-fill text-success me-2"></i>
                                <small class="text-muted">客户</small>
                            </div>
                            <span class="fw-bold text-success" id="totalGuests">0</span>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="d-flex align-items-center justify-content-between p-2 bg-light rounded">
                            <div class="d-flex align-items-center">
                                <i class="bi bi-person-badge-fill text-info me-2"></i>
                                <small class="text-muted">员工</small>
                            </div>
                            <span class="fw-bold text-info" id="totalEmployees">0</span>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="d-flex align-items-center justify-content-between p-2 bg-light rounded">
                            <div class="d-flex align-items-center">
                                <i class="bi bi-currency-dollar text-warning me-2"></i>
                                <small class="text-muted">月利润</small>
                            </div>
                            <span class="fw-bold text-warning" id="monthlyProfit">¥0</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>


    </div>

    <!-- 右侧：控制面板 -->
    <div class="col-lg-4">
        <!-- 时间控制面板 - 参考SimCity风格 -->
        <div class="card border-0 shadow-sm mb-2" style="border-radius: 10px;">
            <div class="card-header bg-gradient text-white py-2" style="background: linear-gradient(90deg, #00b894 0%, #00a085 100%); border-radius: 10px 10px 0 0;">
                <h6 class="mb-0 fw-bold" style="font-size: 0.85rem;">
                    <i class="bi bi-clock-fill me-2"></i>时间控制中心
                </h6>
            </div>
            <div class="card-body py-2">
                <div class="row g-2 mb-2">
                    <div class="col-6">
                        <div class="text-center p-2 rounded" style="background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);">
                            <small class="text-white d-block" style="font-size: 0.7rem;">状态</small>
                            <span class="badge bg-white text-dark fw-bold" id="timeStatus" style="font-size: 0.7rem;">{{ '运行中' if hotel.time_running else '已暂停' }}</span>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="text-center p-2 rounded" style="background: linear-gradient(135deg, #fd79a8 0%, #e84393 100%);">
                            <small class="text-white d-block" style="font-size: 0.7rem;">速度</small>
                            <span class="badge bg-white text-dark fw-bold" id="timeSpeed" style="font-size: 0.7rem;">{{ hotel.time_speed or 1 }}倍速</span>
                        </div>
                    </div>
                </div>
                <div class="row g-1">
                    <div class="col-4">
                        <button type="button" class="btn btn-sm btn-outline-warning w-100" id="toggleTimeBtn" style="font-size: 0.7rem; padding: 0.3rem;">
                            <i class="bi bi-pause-fill"></i>
                        </button>
                    </div>
                    <div class="col-4">
                        <button type="button" class="btn btn-sm btn-outline-primary w-100" id="toggleSpeedBtn" style="font-size: 0.7rem; padding: 0.3rem;">
                            <i class="bi bi-speedometer2"></i>
                        </button>
                    </div>
                    <div class="col-4">
                        <button type="button" class="btn btn-sm btn-outline-success w-100" id="advanceTimeBtn" style="font-size: 0.7rem; padding: 0.3rem;">
                            <i class="bi bi-skip-forward-fill"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
                    </button>
                </div>
            </div>
        </div>

        <!-- 游戏管理面板 -->
        <div class="card border-0 shadow-sm mb-2">
            <div class="card-body py-2">
                <h6 class="card-title mb-2" style="font-size: 0.9rem;">
                    <i class="bi bi-gear-fill text-primary me-1" style="font-size: 0.8rem;"></i>游戏管理
                </h6>
                <div class="d-grid gap-1">
                    <button type="button" class="btn btn-outline-success" onclick="saveGame()" style="font-size: 0.75rem; padding: 0.25rem 0.5rem;">
                        <i class="bi bi-save me-1" style="font-size: 0.7rem;"></i>保存游戏
                    </button>
                    <button type="button" class="btn btn-outline-info" onclick="loadGame()" style="font-size: 0.75rem; padding: 0.25rem 0.5rem;">
                        <i class="bi bi-folder-open me-1" style="font-size: 0.7rem;"></i>读取存档
                    </button>
                    <button type="button" class="btn btn-outline-danger" onclick="restartGame()" style="font-size: 0.75rem; padding: 0.25rem 0.5rem;">
                        <i class="bi bi-arrow-clockwise me-1" style="font-size: 0.7rem;"></i>重新开始
                    </button>
                    <button type="button" class="btn btn-outline-secondary" onclick="showHelp()" style="font-size: 0.75rem; padding: 0.25rem 0.5rem;">
                        <i class="bi bi-question-circle me-1" style="font-size: 0.7rem;"></i>游戏帮助
                    </button>
                </div>
            </div>
        </div>

        <!-- 快捷管理面板 - 参考Theme Hospital风格 -->
        <div class="card border-0 shadow-sm" style="border-radius: 10px;">
            <div class="card-header bg-gradient text-white py-2" style="background: linear-gradient(90deg, #fd79a8 0%, #e84393 100%); border-radius: 10px 10px 0 0;">
                <h6 class="mb-0 fw-bold" style="font-size: 0.85rem;">
                    <i class="bi bi-grid-3x3-gap-fill me-2"></i>管理中心
                </h6>
            </div>
            <div class="card-body py-2">
                <div class="row g-1">
                    <div class="col-4">
                        <a href="{{ url_for('departments.management') }}" class="btn w-100 d-flex flex-column align-items-center p-2 text-decoration-none"
                           style="background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%); border-radius: 8px; color: white;">
                            <i class="bi bi-diagram-3-fill mb-1" style="font-size: 1rem;"></i>
                            <small style="font-size: 0.65rem;">部门</small>
                        </a>
                    </div>
                    <div class="col-4">
                        <a href="{{ url_for('employees.management') }}" class="btn w-100 d-flex flex-column align-items-center p-2 text-decoration-none"
                           style="background: linear-gradient(135deg, #55efc4 0%, #00b894 100%); border-radius: 8px; color: white;">
                            <i class="bi bi-people-fill mb-1" style="font-size: 1rem;"></i>
                            <small style="font-size: 0.65rem;">员工</small>
                        </a>
                    </div>
                    <div class="col-4">
                        <a href="{{ url_for('rooms.management') }}" class="btn w-100 d-flex flex-column align-items-center p-2 text-decoration-none"
                           style="background: linear-gradient(135deg, #fd79a8 0%, #e84393 100%); border-radius: 8px; color: white;">
                            <i class="bi bi-door-open-fill mb-1" style="font-size: 1rem;"></i>
                            <small style="font-size: 0.65rem;">房间</small>
                        </a>
                    </div>
                    <div class="col-4">
                        <a href="{{ url_for('finance.management') }}" class="btn w-100 d-flex flex-column align-items-center p-2 text-decoration-none"
                           style="background: linear-gradient(135deg, #ffeaa7 0%, #fdcb6e 100%); border-radius: 8px; color: white;">
                            <i class="bi bi-cash-stack mb-1" style="font-size: 1rem;"></i>
                            <small style="font-size: 0.65rem;">财务</small>
                        </a>
                    </div>
                    <div class="col-4">
                        <a href="{{ url_for('hotel.management') }}" class="btn w-100 d-flex flex-column align-items-center p-2 text-decoration-none"
                           style="background: linear-gradient(135deg, #a29bfe 0%, #6c5ce7 100%); border-radius: 8px; color: white;">
                            <i class="bi bi-arrow-up-circle-fill mb-1" style="font-size: 1rem;"></i>
                            <small style="font-size: 0.65rem;">升级</small>
                        </a>
                    </div>
                    <div class="col-4">
                        <a href="{{ url_for('marketing.management') }}" class="btn w-100 d-flex flex-column align-items-center p-2 text-decoration-none"
                           style="background: linear-gradient(135deg, #fab1a0 0%, #e17055 100%); border-radius: 8px; color: white;">
                            <i class="bi bi-megaphone-fill mb-1" style="font-size: 1rem;"></i>
                            <small style="font-size: 0.65rem;">营销</small>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 入住率趋势折线图 -->
<div class="row">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5 class="mb-0">
                        <i class="bi bi-graph-up-arrow text-primary me-2"></i>各房型入住率趋势
                    </h5>
                    <div class="d-flex gap-2">
                        <span class="badge bg-light text-dark">
                            <i class="bi bi-circle-fill text-danger me-1" style="font-size: 0.5rem;"></i>单人间
                        </span>
                        <span class="badge bg-light text-dark">
                            <i class="bi bi-circle-fill text-primary me-1" style="font-size: 0.5rem;"></i>标准间
                        </span>
                    </div>
                </div>
                <!-- 图表容器 -->
                <div class="bg-light rounded p-3" style="height: 350px; position: relative;">
                    <canvas id="occupancyChart" width="100%" height="350"></canvas>
                </div>
                <div class="mt-3 text-center">
                    <small class="text-muted">
                        <i class="bi bi-info-circle me-1"></i>
                        显示各房型最近10天的入住率变化趋势，数据实时更新
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // 游戏状态管理
    let gameState = {
        isTimeRunning: true,
        timeSpeed: 1,
        updateInterval: null,
        lastUpdateTime: 0,
        chart: null
    };

    // 页面初始化
    document.addEventListener('DOMContentLoaded', function() {
        console.log('🚀 酒店管理系统初始化...');

        // 初始化图表
        initChart();

        // 立即更新数据
        updateAllData();

        // 设置定时更新（每5秒）
        gameState.updateInterval = setInterval(updateAllData, 5000);

        // 绑定事件处理器
        bindEventHandlers();

        console.log('✅ 系统初始化完成');
    });

    // 绑定所有事件处理器
    function bindEventHandlers() {
        // 时间控制按钮
        document.getElementById('toggleTimeBtn')?.addEventListener('click', toggleTime);
        document.getElementById('toggleSpeedBtn')?.addEventListener('click', toggleTimeSpeed);
        document.getElementById('advanceTimeBtn')?.addEventListener('click', advanceTime);
    }

    // 更新所有数据
    async function updateAllData() {
        try {
            await Promise.all([
                updateHotelInfo(),
                updateOccupancyData()
            ]);

            gameState.lastUpdateTime = Date.now();
            console.log('📊 数据更新完成');
        } catch (error) {
            console.error('❌ 数据更新失败:', error);
        }
    }

    // 更新酒店信息
    async function updateHotelInfo() {
        try {
            const data = await apiRequest('/api/hotel_info');

            if (data.success) {
                // 更新基本信息
                updateElement('hotelName', data.hotel_name);
                updateElement('hotelLevel', data.level);
                updateElement('hotelMoney', formatCurrency(data.money));
                updateElement('currentDate', data.current_date);
                updateElement('daysElapsed', data.days_elapsed + '天');
                updateElement('reputation', data.reputation);
                updateElement('reputationLevel', data.reputation_level);
                updateElement('satisfaction', data.satisfaction.toFixed(1) + '分');

                // 更新运营概览
                updateElement('totalRooms', data.total_rooms || 0);
                updateElement('totalGuests', Math.round(data.total_guests || 0));
                updateElement('totalEmployees', data.employee_count || 0);
                updateElement('monthlyProfit', formatCurrency(data.monthly_profit || 0));

                // 更新时间状态
                gameState.isTimeRunning = data.time_running;
                gameState.timeSpeed = data.time_speed || 1;
                updateTimeControls();

                // 更新满意度进度条
                updateSatisfactionBar(data.satisfaction);
            }
        } catch (error) {
            console.error('获取酒店信息失败:', error);
        }
    }

    // 更新时间控制显示
    function updateTimeControls() {
        const timeStatus = document.getElementById('timeStatus');
        const timeSpeed = document.getElementById('timeSpeed');
        const toggleBtn = document.getElementById('toggleTimeBtn');

        if (timeStatus) {
            timeStatus.textContent = gameState.isTimeRunning ? '运行中' : '已暂停';
            timeStatus.className = 'badge ' + (gameState.isTimeRunning ? 'bg-success' : 'bg-danger');
        }

        if (timeSpeed) {
            timeSpeed.textContent = gameState.timeSpeed + '倍速';
        }

        if (toggleBtn) {
            toggleBtn.innerHTML = gameState.isTimeRunning ?
                '<i class="bi bi-pause-fill"></i>' :
                '<i class="bi bi-play-fill"></i>';
            toggleBtn.title = gameState.isTimeRunning ? '暂停时间' : '继续时间';
        }
    }

    // 更新满意度进度条
    function updateSatisfactionBar(satisfaction) {
        const bar = document.getElementById('satisfactionBar');
        if (bar) {
            bar.style.width = satisfaction + '%';
            bar.setAttribute('aria-valuenow', satisfaction);
        }
    }

    // 初始化图表
    function initChart() {
        const ctx = document.getElementById('occupancyChart');
        if (!ctx) return;

        gameState.chart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: [],
                datasets: []
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: '各房型入住率趋势'
                    },
                    legend: {
                        display: true,
                        position: 'top'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 100,
                        ticks: {
                            callback: function(value) {
                                return value + '%';
                            }
                        }
                    }
                },
                elements: {
                    line: {
                        tension: 0.4
                    },
                    point: {
                        radius: 3,
                        hoverRadius: 6
                    }
                }
            }
        });
    }

    // 更新入住率图表数据
    async function updateOccupancyData() {
        try {
            const data = await apiRequest('/api/occupancy_data');

            const roomTypes = Object.keys(data);
            if (roomTypes.length === 0 || !gameState.chart) return;

            // 获取日期标签
            const labels = data[roomTypes[0]]?.map(item => {
                const date = new Date(item.date);
                return (date.getMonth() + 1) + '/' + date.getDate();
            }) || [];

            // 生成颜色
            const colors = [
                'rgb(255, 99, 132)',   // 红色
                'rgb(54, 162, 235)',   // 蓝色
                'rgb(255, 205, 86)',   // 黄色
                'rgb(75, 192, 192)',   // 青色
                'rgb(153, 102, 255)',  // 紫色
                'rgb(255, 159, 64)',   // 橙色
                'rgb(199, 199, 199)',  // 灰色
                'rgb(83, 102, 255)'    // 靛蓝色
            ];

            // 构建数据集
            const datasets = roomTypes.map((roomType, index) => ({
                label: roomType,
                data: data[roomType]?.map(item => item.rate) || [],
                borderColor: colors[index % colors.length],
                backgroundColor: colors[index % colors.length] + '20',
                fill: false
            }));

            // 更新图表
            gameState.chart.data.labels = labels;
            gameState.chart.data.datasets = datasets;
            gameState.chart.update('none'); // 无动画更新，提高性能

            console.log('📈 入住率图表更新完成');
        } catch (error) {
            console.error('获取入住率数据失败:', error);
        }
    }

    // 时间控制函数
    async function toggleTime() {
        try {
            const data = await apiRequest('/api/toggle_time', { method: 'POST' });
            if (data.success) {
                showMessage(data.message, 'success');
                await updateAllData();
            }
        } catch (error) {
            showMessage('时间控制操作失败', 'error');
        }
    }

    async function toggleTimeSpeed() {
        try {
            const data = await apiRequest('/api/toggle_time_speed', { method: 'POST' });
            if (data.success) {
                showMessage(data.message, 'success');
                await updateAllData();
            }
        } catch (error) {
            showMessage('速度切换操作失败', 'error');
        }
    }

    async function advanceTime() {
        try {
            const data = await apiRequest('/api/advance_time', { method: 'POST' });
            if (data.success) {
                showMessage(data.message, 'success');
                await updateAllData();
            }
        } catch (error) {
            showMessage('时间推进操作失败', 'error');
        }
    }

    // 游戏管理函数
    function editHotelName() {
        const currentName = document.getElementById('hotelName').textContent;
        const newName = prompt('请输入新的酒店名称:', currentName);

        if (newName && newName.trim() && newName.trim() !== currentName) {
            apiRequest('/api/update_hotel_name', {
                method: 'POST',
                body: JSON.stringify({name: newName.trim()})
            }).then(data => {
                if (data.success) {
                    updateElement('hotelName', newName.trim());
                    showMessage('酒店名称已更新', 'success');
                }
            }).catch(() => {
                showMessage('更新酒店名称失败', 'error');
            });
        }
    }

    function saveGame() {
        const saveName = prompt('请输入存档名称:', '存档_' + new Date().toLocaleString());
        if (saveName && saveName.trim()) {
            apiRequest('/api/save_game', {
                method: 'POST',
                body: JSON.stringify({save_name: saveName.trim()})
            }).then(data => {
                if (data.success) {
                    showMessage('游戏保存成功', 'success');
                }
            }).catch(() => {
                showMessage('保存游戏失败', 'error');
            });
        }
    }

    function loadGame() {
        showMessage('读取存档功能开发中...', 'info');
    }

    function restartGame() {
        confirmAction('确定要重新开始游戏吗？当前进度将会完全丢失！', () => {
            apiRequest('/api/restart_game', { method: 'POST' })
            .then(data => {
                if (data.success) {
                    showMessage('游戏重新开始，即将刷新页面...', 'success');
                    setTimeout(() => location.reload(), 2000);
                }
            }).catch(() => {
                showMessage('重新开始游戏失败', 'error');
            });
        });
    }

    function showHelp() {
        const helpText = `酒店管理系统 - 游戏帮助

🎮 基本操作：
• 时间控制：可以暂停/继续时间，切换1倍速/2倍速
• 手动推进：点击推进按钮手动推进时间

🏨 酒店管理：
• 升级条件：需要满足资金、声望、运营天数、满意度要求
• 部门效果：不同部门解锁后有特殊效果和收入加成
• 满意度：影响声望值变化和客户入住意愿

💰 经营策略：
• 合理招聘员工，避免部门过度繁忙
• 投资营销活动提升入住率
• 平衡收入和支出，确保资金流健康

🏆 成就系统：
• 完成各种成就可获得声望值奖励
• 声望等级影响入住率和随机事件

💾 存档功能：
• 支持保存和读取游戏进度
• 可以导出游戏数据进行备份`;

        alert(helpText);
    }

    function exportData() {
        showMessage('数据导出功能开发中...', 'info');
    }

    function showSettings() {
        showMessage('游戏设置功能开发中...', 'info');
    }

    // 页面卸载时清理
    window.addEventListener('beforeunload', function() {
        if (gameState.updateInterval) {
            clearInterval(gameState.updateInterval);
        }
    });
</script>
{% endblock %}

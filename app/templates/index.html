{% extends "base.html" %}

{% block title %}{{ hotel.name }} - 酒店管理系统{% endblock %}

{% block content %}
<div class="container-fluid px-4">
    <!-- 顶部导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark rounded mb-4">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="bi bi-building-fill"></i>
                <span id="hotelName">{{ hotel.name }}</span>
                <span class="badge bg-warning text-dark ms-2" id="hotelLevel">{{ hotel.level }}星</span>
            </a>
            
            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="bi bi-gear-fill"></i> 游戏管理
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="#" onclick="saveGame()"><i class="bi bi-save"></i> 保存游戏</a></li>
                        <li><a class="dropdown-item" href="#" onclick="loadGame()"><i class="bi bi-folder-open"></i> 读取存档</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item text-danger" href="#" onclick="restartGame()"><i class="bi bi-arrow-clockwise"></i> 重新开始</a></li>
                    </ul>
                </div>
                <button class="btn btn-outline-light btn-sm ms-2" onclick="editHotelName()">
                    <i class="bi bi-pencil"></i> 改名
                </button>
            </div>
        </div>
    </nav>

    <!-- 主要信息面板 -->
    <div class="row mb-4">
        <!-- 左侧：核心数据 -->
        <div class="col-lg-8">
            <div class="row">
                <!-- 资金状况 -->
                <div class="col-md-6 mb-3">
                    <div class="card border-success h-100">
                        <div class="card-header bg-success text-white">
                            <h6 class="mb-0"><i class="bi bi-cash-stack"></i> 财务状况</h6>
                        </div>
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span>当前资金</span>
                                <h4 class="text-success mb-0" id="hotelMoney">¥{{ "{:,}".format(hotel.money) }}</h4>
                            </div>
                            <div class="d-flex justify-content-between align-items-center">
                                <span>月度利润</span>
                                <h5 class="text-info mb-0" id="monthlyProfit">¥0</h5>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 运营数据 -->
                <div class="col-md-6 mb-3">
                    <div class="card border-info h-100">
                        <div class="card-header bg-info text-white">
                            <h6 class="mb-0"><i class="bi bi-graph-up"></i> 运营数据</h6>
                        </div>
                        <div class="card-body">
                            <div class="row text-center">
                                <div class="col-4">
                                    <div class="border-end">
                                        <h5 class="text-primary mb-0" id="totalRooms">0</h5>
                                        <small class="text-muted">房间</small>
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div class="border-end">
                                        <h5 class="text-success mb-0" id="totalGuests">0</h5>
                                        <small class="text-muted">客户</small>
                                    </div>
                                </div>
                                <div class="col-4">
                                    <h5 class="text-warning mb-0" id="totalEmployees">0</h5>
                                    <small class="text-muted">员工</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 满意度和声望 -->
                <div class="col-md-6 mb-3">
                    <div class="card border-warning h-100">
                        <div class="card-header bg-warning text-dark">
                            <h6 class="mb-0"><i class="bi bi-heart-fill"></i> 客户满意度</h6>
                        </div>
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span>满意度</span>
                                <h4 class="mb-0" id="satisfaction">{{ "%.1f"|format(hotel.satisfaction) }}分</h4>
                            </div>
                            <div class="progress mb-2">
                                <div class="progress-bar bg-warning" role="progressbar" 
                                     style="width: {{ hotel.satisfaction }}%" id="satisfactionBar">
                                </div>
                            </div>
                            <small class="text-muted">影响声望值变化和客户入住率</small>
                        </div>
                    </div>
                </div>

                <!-- 声望系统 -->
                <div class="col-md-6 mb-3">
                    <div class="card border-secondary h-100">
                        <div class="card-header bg-secondary text-white">
                            <h6 class="mb-0"><i class="bi bi-star-fill"></i> 声望系统</h6>
                        </div>
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span>声望值</span>
                                <h4 class="text-secondary mb-0" id="reputation">{{ hotel.reputation }}</h4>
                            </div>
                            <div class="d-flex justify-content-between align-items-center">
                                <span>声望等级</span>
                                <span class="badge bg-secondary" id="reputationLevel">{{ hotel.reputation_level }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 右侧：时间控制 -->
        <div class="col-lg-4">
            <div class="card border-primary h-100">
                <div class="card-header bg-primary text-white">
                    <h6 class="mb-0"><i class="bi bi-clock-fill"></i> 时间控制中心</h6>
                </div>
                <div class="card-body">
                    <!-- 时间信息 -->
                    <div class="text-center mb-3">
                        <h3 class="text-primary mb-1" id="currentDate">{{ hotel.date.strftime('%Y-%m-%d') }}</h3>
                        <p class="text-muted mb-0">运营第 <span id="daysElapsed">{{ hotel.days_elapsed }}</span> 天</p>
                    </div>

                    <!-- 时间状态 -->
                    <div class="row text-center mb-3">
                        <div class="col-6">
                            <div class="border-end">
                                <h6 class="mb-1">运行状态</h6>
                                <span class="badge" id="timeStatusBadge">运行中</span>
                            </div>
                        </div>
                        <div class="col-6">
                            <h6 class="mb-1">运行速度</h6>
                            <span class="badge bg-info" id="timeSpeedBadge">{{ hotel.time_speed or 1 }}倍速</span>
                        </div>
                    </div>

                    <!-- 时间控制按钮 -->
                    <div class="d-grid gap-2">
                        <button class="btn btn-warning" id="toggleTimeBtn">
                            <i class="bi bi-pause-fill"></i> 暂停时间
                        </button>
                        <button class="btn btn-info" id="toggleSpeedBtn">
                            <i class="bi bi-speedometer2"></i> 切换速度
                        </button>
                        <button class="btn btn-success" id="advanceTimeBtn">
                            <i class="bi bi-skip-forward-fill"></i> 推进一天
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 快捷管理面板 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-dark text-white">
                    <h6 class="mb-0"><i class="bi bi-lightning-fill"></i> 快捷管理面板</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-lg-2 col-md-4 col-6 mb-3">
                            <a href="{{ url_for('departments.management') }}" class="btn btn-outline-primary w-100 h-100 d-flex flex-column justify-content-center">
                                <i class="bi bi-diagram-3-fill fs-1 mb-2"></i>
                                <span>部门管理</span>
                            </a>
                        </div>
                        <div class="col-lg-2 col-md-4 col-6 mb-3">
                            <a href="{{ url_for('employees.management') }}" class="btn btn-outline-success w-100 h-100 d-flex flex-column justify-content-center">
                                <i class="bi bi-people-fill fs-1 mb-2"></i>
                                <span>员工管理</span>
                            </a>
                        </div>
                        <div class="col-lg-2 col-md-4 col-6 mb-3">
                            <a href="{{ url_for('rooms.management') }}" class="btn btn-outline-info w-100 h-100 d-flex flex-column justify-content-center">
                                <i class="bi bi-door-open-fill fs-1 mb-2"></i>
                                <span>房间管理</span>
                            </a>
                        </div>
                        <div class="col-lg-2 col-md-4 col-6 mb-3">
                            <a href="{{ url_for('finance.management') }}" class="btn btn-outline-warning w-100 h-100 d-flex flex-column justify-content-center">
                                <i class="bi bi-cash-stack fs-1 mb-2"></i>
                                <span>财务管理</span>
                            </a>
                        </div>
                        <div class="col-lg-2 col-md-4 col-6 mb-3">
                            <a href="{{ url_for('hotel.management') }}" class="btn btn-outline-dark w-100 h-100 d-flex flex-column justify-content-center">
                                <i class="bi bi-arrow-up-circle-fill fs-1 mb-2"></i>
                                <span>酒店升级</span>
                            </a>
                        </div>
                        <div class="col-lg-2 col-md-4 col-6 mb-3">
                            <a href="{{ url_for('marketing.management') }}" class="btn btn-outline-secondary w-100 h-100 d-flex flex-column justify-content-center">
                                <i class="bi bi-megaphone-fill fs-1 mb-2"></i>
                                <span>营销管理</span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 部门状态概览 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-gradient" style="background: linear-gradient(45deg, #6f42c1, #e83e8c);">
                    <h6 class="mb-0 text-white"><i class="bi bi-building-gear"></i> 部门状态概览</h6>
                </div>
                <div class="card-body">
                    <div class="row" id="departmentContainer">
                        {% for department in departments %}
                        <div class="col-lg-3 col-md-4 col-sm-6 mb-3">
                            <div class="card {% if department.is_unlocked %}border-success{% else %}border-secondary{% endif %} h-100">
                                <div class="card-body text-center">
                                    <h6 class="card-title">
                                        {% if department.is_unlocked %}
                                        <i class="bi bi-check-circle-fill text-success"></i>
                                        {% else %}
                                        <i class="bi bi-lock-fill text-secondary"></i>
                                        {% endif %}
                                        {{ department.name }}
                                    </h6>
                                    
                                    {% if department.is_unlocked %}
                                    <div class="mt-2">
                                        <small class="text-muted">繁忙度</small>
                                        <div class="progress mt-1">
                                            <div class="progress-bar" role="progressbar" style="width: 0%" 
                                                 id="busy-{{ department.name }}">0%</div>
                                        </div>
                                    </div>
                                    {% else %}
                                    <div class="mt-2">
                                        <small class="text-muted">解锁费用</small>
                                        <p class="text-secondary mb-0">¥{{ "{:,}".format(department.unlock_cost) }}</p>
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 入住率数据 -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h6 class="mb-0"><i class="bi bi-graph-up-arrow"></i> 最近10天入住率趋势</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover" id="occupancyTable">
                            <thead class="table-dark">
                                <tr id="occupancyTableHead">
                                    <th>房间类型</th>
                                    <!-- 动态生成日期列 -->
                                </tr>
                            </thead>
                            <tbody id="occupancyTableBody">
                                <!-- 动态生成数据行 -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 消息提示容器 -->
<div id="messageContainer" style="position: fixed; top: 20px; right: 20px; z-index: 9999;"></div>
{% endblock %}

{% block scripts %}
<script>
    // 全局变量
    let gameData = {
        isTimeRunning: true,
        timeSpeed: 1,
        updateInterval: null
    };

    // 页面加载完成后初始化
    document.addEventListener('DOMContentLoaded', function() {
        console.log('页面加载完成，开始初始化...');

        // 初始化数据
        updateAllData();

        // 设置定时更新（每5秒）
        gameData.updateInterval = setInterval(updateAllData, 5000);

        // 绑定所有事件处理器
        bindEventHandlers();

        console.log('初始化完成');
    });

    // 绑定事件处理器
    function bindEventHandlers() {
        console.log('绑定事件处理器...');

        // 时间控制按钮
        const toggleTimeBtn = document.getElementById('toggleTimeBtn');
        const toggleSpeedBtn = document.getElementById('toggleSpeedBtn');
        const advanceTimeBtn = document.getElementById('advanceTimeBtn');

        if (toggleTimeBtn) {
            toggleTimeBtn.addEventListener('click', function(e) {
                e.preventDefault();
                console.log('点击暂停/继续按钮');
                toggleTime();
            });
        }

        if (toggleSpeedBtn) {
            toggleSpeedBtn.addEventListener('click', function(e) {
                e.preventDefault();
                console.log('点击切换速度按钮');
                toggleTimeSpeed();
            });
        }

        if (advanceTimeBtn) {
            advanceTimeBtn.addEventListener('click', function(e) {
                e.preventDefault();
                console.log('点击推进时间按钮');
                advanceTime();
            });
        }

        console.log('事件处理器绑定完成');
    }

    // 更新所有数据
    function updateAllData() {
        console.log('开始更新数据...');
        fetchHotelInfo();
        fetchOccupancyData();
    }

    // 获取酒店基本信息
    function fetchHotelInfo() {
        fetch('/api/hotel_info')
        .then(response => {
            if (!response.ok) {
                throw new Error('网络响应错误: ' + response.status);
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                updateHotelDisplay(data);
            } else {
                console.error('获取酒店信息失败:', data.message);
            }
        })
        .catch(error => {
            console.error('获取酒店信息失败:', error);
        });
    }

    // 更新酒店信息显示
    function updateHotelDisplay(data) {
        try {
            // 基本信息
            updateElement('hotelName', data.hotel_name);
            updateElement('hotelLevel', data.level + '星');
            updateElement('hotelMoney', '¥' + Number(data.money).toLocaleString());
            updateElement('currentDate', data.current_date);
            updateElement('daysElapsed', data.days_elapsed);
            updateElement('reputation', data.reputation);
            updateElement('reputationLevel', data.reputation_level);

            // 满意度
            if (data.satisfaction !== undefined) {
                updateElement('satisfaction', data.satisfaction.toFixed(1) + '分');
                const satisfactionBar = document.getElementById('satisfactionBar');
                if (satisfactionBar) {
                    satisfactionBar.style.width = data.satisfaction + '%';
                }
            }

            // 时间状态
            gameData.isTimeRunning = data.time_running;
            gameData.timeSpeed = data.time_speed || 1;

            const timeStatusBadge = document.getElementById('timeStatusBadge');
            const timeSpeedBadge = document.getElementById('timeSpeedBadge');
            const toggleTimeBtn = document.getElementById('toggleTimeBtn');

            if (timeStatusBadge) {
                timeStatusBadge.textContent = gameData.isTimeRunning ? '运行中' : '已暂停';
                timeStatusBadge.className = 'badge ' + (gameData.isTimeRunning ? 'bg-success' : 'bg-danger');
            }

            if (timeSpeedBadge) {
                timeSpeedBadge.textContent = gameData.timeSpeed + '倍速';
            }

            if (toggleTimeBtn) {
                toggleTimeBtn.innerHTML = gameData.isTimeRunning ?
                    '<i class="bi bi-pause-fill"></i> 暂停时间' :
                    '<i class="bi bi-play-fill"></i> 继续时间';
            }

            // 运营概览
            updateElement('totalRooms', data.total_rooms || 0);
            updateElement('totalGuests', Math.round(data.total_guests || 0));
            updateElement('totalEmployees', data.employee_count || 0);
            updateElement('monthlyProfit', '¥' + Number(data.monthly_profit || 0).toLocaleString());

            console.log('酒店信息更新完成');
        } catch (error) {
            console.error('更新酒店信息显示时出错:', error);
        }
    }

    // 辅助函数：更新元素内容
    function updateElement(id, content) {
        const element = document.getElementById(id);
        if (element) {
            element.textContent = content;
        }
    }

    // 获取入住率数据
    function fetchOccupancyData() {
        fetch('/api/occupancy_data')
        .then(response => response.json())
        .then(data => {
            updateOccupancyTable(data);
        })
        .catch(error => {
            console.error('获取入住率数据失败:', error);
        });
    }

    // 更新入住率表格
    function updateOccupancyTable(occupancyData) {
        try {
            const roomTypes = Object.keys(occupancyData);
            if (roomTypes.length === 0) return;

            const thead = document.getElementById('occupancyTableHead');
            const tbody = document.getElementById('occupancyTableBody');

            if (!thead || !tbody) return;

            // 清空并重建表头
            thead.innerHTML = '<th>房间类型</th>';

            // 添加日期列
            if (occupancyData[roomTypes[0]] && occupancyData[roomTypes[0]].length > 0) {
                const dates = occupancyData[roomTypes[0]].map(item => item.date);
                dates.forEach(date => {
                    const th = document.createElement('th');
                    th.textContent = date.split('-')[2] + '日';
                    thead.appendChild(th);
                });

                // 重建表格内容
                tbody.innerHTML = '';
                roomTypes.forEach(roomType => {
                    const tr = document.createElement('tr');

                    // 房间类型列
                    const roomTypeTd = document.createElement('td');
                    roomTypeTd.textContent = roomType;
                    roomTypeTd.className = 'fw-bold';
                    tr.appendChild(roomTypeTd);

                    // 入住率数据列
                    occupancyData[roomType].forEach(item => {
                        const td = document.createElement('td');
                        td.textContent = item.rate + '%';

                        // 根据入住率设置颜色
                        if (item.rate >= 80) {
                            td.className = 'text-success fw-bold';
                        } else if (item.rate >= 50) {
                            td.className = 'text-warning fw-bold';
                        } else {
                            td.className = 'text-danger fw-bold';
                        }

                        tr.appendChild(td);
                    });

                    tbody.appendChild(tr);
                });
            }

            console.log('入住率表格更新完成');
        } catch (error) {
            console.error('更新入住率表格时出错:', error);
        }
    }

    // 时间控制函数
    function toggleTime() {
        console.log('执行暂停/继续时间操作...');

        fetch('/api/toggle_time', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showMessage(data.message, 'success');
                updateAllData();
            } else {
                showMessage('操作失败: ' + data.message, 'error');
            }
        })
        .catch(error => {
            console.error('切换时间状态失败:', error);
            showMessage('切换时间状态失败', 'error');
        });
    }

    function toggleTimeSpeed() {
        console.log('执行切换时间速度操作...');

        fetch('/api/toggle_time_speed', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showMessage('时间速度已切换为 ' + data.time_speed + '倍速', 'success');
                updateAllData();
            } else {
                showMessage('切换时间速度失败: ' + data.message, 'error');
            }
        })
        .catch(error => {
            console.error('切换时间速度失败:', error);
            showMessage('切换时间速度失败', 'error');
        });
    }

    function advanceTime() {
        console.log('执行推进时间操作...');

        fetch('/api/advance_time', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showMessage(data.message, 'success');
                updateAllData();
            } else {
                showMessage('推进时间失败: ' + data.message, 'error');
            }
        })
        .catch(error => {
            console.error('推进时间失败:', error);
            showMessage('推进时间失败', 'error');
        });
    }

    // 游戏管理函数
    function saveGame() {
        const saveName = prompt('请输入存档名称:', '存档_' + new Date().toLocaleString());
        if (saveName && saveName.trim()) {
            fetch('/api/save_game', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({save_name: saveName.trim()})
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showMessage('游戏保存成功: ' + saveName, 'success');
                } else {
                    showMessage('保存失败: ' + data.message, 'error');
                }
            })
            .catch(error => {
                console.error('保存游戏失败:', error);
                showMessage('保存游戏失败', 'error');
            });
        }
    }

    function loadGame() {
        showMessage('读取存档功能开发中...', 'info');
    }

    function restartGame() {
        if (confirm('确定要重新开始游戏吗？\n当前进度将会完全丢失！\n\n此操作不可撤销！')) {
            fetch('/api/restart_game', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showMessage('游戏已重新开始，即将刷新页面...', 'success');
                    setTimeout(() => location.reload(), 2000);
                } else {
                    showMessage('重新开始失败: ' + data.message, 'error');
                }
            })
            .catch(error => {
                console.error('重新开始游戏失败:', error);
                showMessage('重新开始游戏失败', 'error');
            });
        }
    }

    function editHotelName() {
        const currentName = document.getElementById('hotelName').textContent;
        const newName = prompt('请输入新的酒店名称:', currentName);

        if (newName && newName.trim() && newName.trim() !== currentName) {
            fetch('/api/update_hotel_name', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({name: newName.trim()})
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    updateElement('hotelName', newName.trim());
                    showMessage('酒店名称已更新为: ' + newName.trim(), 'success');
                } else {
                    showMessage('更新失败: ' + data.message, 'error');
                }
            })
            .catch(error => {
                console.error('更新酒店名称失败:', error);
                showMessage('更新酒店名称失败', 'error');
            });
        }
    }

    // 显示消息提示
    function showMessage(message, type = 'info') {
        const container = document.getElementById('messageContainer');
        if (!container) return;

        const alertDiv = document.createElement('div');
        const alertClass = type === 'error' ? 'danger' : type === 'success' ? 'success' : 'info';

        alertDiv.className = `alert alert-${alertClass} alert-dismissible fade show mb-2`;
        alertDiv.style.cssText = 'min-width: 300px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);';
        alertDiv.innerHTML = `
            <strong>${type === 'error' ? '错误' : type === 'success' ? '成功' : '提示'}:</strong> ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        container.appendChild(alertDiv);

        // 3秒后自动消失
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, 3000);

        console.log(`显示消息: [${type}] ${message}`);
    }

    // 页面卸载时清理定时器
    window.addEventListener('beforeunload', function() {
        if (gameData.updateInterval) {
            clearInterval(gameData.updateInterval);
        }
    });
</script>
{% endblock %}

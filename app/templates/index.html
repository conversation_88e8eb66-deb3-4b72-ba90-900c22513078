{% extends "base.html" %}

{% block title %}{{ hotel.name }} - 酒店管理系统{% endblock %}

{% block content %}
<!-- 页面标题区域 -->
<div class="row mb-3">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <h1 class="h3 mb-0">
                <i class="bi bi-building-fill text-primary me-2"></i>
                <span id="hotelName" class="text-primary" style="cursor: pointer;" onclick="editHotelName()">{{ hotel.name }}</span>
                <small class="text-muted ms-2">- 酒店管理系统</small>
            </h1>
            <div class="text-muted">
                <small>当前版本：v1.0 | 开发：Augment Agent</small>
            </div>
        </div>
    </div>
</div>

<!-- 酒店基本信息卡片 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-info-circle-fill me-2"></i>酒店基本信息
                </h5>
            </div>
            <div class="card-body">
                <!-- 第一行：基本数据 -->
                <div class="row mb-3">
                    <div class="col-lg-2 col-md-4 col-6 mb-2">
                        <div class="text-center">
                            <div class="text-muted small">酒店等级</div>
                            <div class="h4 text-warning mb-0">
                                <i class="bi bi-star-fill"></i>
                                <span id="hotelLevel">{{ hotel.level }}</span>星
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-2 col-md-4 col-6 mb-2">
                        <div class="text-center">
                            <div class="text-muted small">资金余额</div>
                            <div class="h4 text-success mb-0" id="hotelMoney">¥{{ "{:,}".format(hotel.money) }}</div>
                        </div>
                    </div>
                    <div class="col-lg-2 col-md-4 col-6 mb-2">
                        <div class="text-center">
                            <div class="text-muted small">当前日期</div>
                            <div class="h4 text-info mb-0" id="currentDate">{{ hotel.date.strftime('%Y-%m-%d') }}</div>
                        </div>
                    </div>
                    <div class="col-lg-2 col-md-4 col-6 mb-2">
                        <div class="text-center">
                            <div class="text-muted small">运营天数</div>
                            <div class="h4 text-primary mb-0" id="daysElapsed">{{ hotel.days_elapsed }}天</div>
                        </div>
                    </div>
                    <div class="col-lg-2 col-md-4 col-6 mb-2">
                        <div class="text-center">
                            <div class="text-muted small">声望值</div>
                            <div class="h4 text-secondary mb-0" id="reputation">{{ hotel.reputation }}</div>
                        </div>
                    </div>
                    <div class="col-lg-2 col-md-4 col-6 mb-2">
                        <div class="text-center">
                            <div class="text-muted small">声望等级</div>
                            <div class="h4 text-dark mb-0">
                                <span class="badge bg-secondary" id="reputationLevel">{{ hotel.reputation_level }}</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 第二行：满意度和时间控制 -->
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <div class="card bg-light">
                            <div class="card-body">
                                <h6 class="card-title">
                                    <i class="bi bi-heart-fill text-danger me-2"></i>客户满意度
                                </h6>
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <span>当前满意度</span>
                                    <span class="h5 mb-0" id="satisfaction">{{ "%.1f"|format(hotel.satisfaction) }}分</span>
                                </div>
                                <div class="progress">
                                    <div class="progress-bar bg-warning" role="progressbar" 
                                         style="width: {{ hotel.satisfaction }}%" id="satisfactionBar">
                                    </div>
                                </div>
                                <small class="text-muted">影响声望值变化和客户入住意愿</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="card bg-light">
                            <div class="card-body">
                                <h6 class="card-title">
                                    <i class="bi bi-clock-fill text-primary me-2"></i>时间控制系统
                                </h6>
                                <div class="row text-center mb-2">
                                    <div class="col-4">
                                        <small class="text-muted">状态</small>
                                        <div>
                                            <span class="badge" id="timeStatus">{{ '运行中' if hotel.time_running else '已暂停' }}</span>
                                        </div>
                                    </div>
                                    <div class="col-4">
                                        <small class="text-muted">速度</small>
                                        <div>
                                            <span class="badge bg-info" id="timeSpeed">{{ hotel.time_speed or 1 }}倍速</span>
                                        </div>
                                    </div>
                                    <div class="col-4">
                                        <small class="text-muted">操作</small>
                                        <div class="btn-group btn-group-sm" role="group">
                                            <button type="button" class="btn btn-outline-warning" id="toggleTimeBtn" title="暂停/继续">
                                                <i class="bi bi-pause-fill"></i>
                                            </button>
                                            <button type="button" class="btn btn-outline-primary" id="toggleSpeedBtn" title="切换速度">
                                                <i class="bi bi-speedometer2"></i>
                                            </button>
                                            <button type="button" class="btn btn-outline-success" id="advanceTimeBtn" title="推进一天">
                                                <i class="bi bi-skip-forward-fill"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 游戏管理功能区域 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-gear-fill me-2"></i>游戏管理功能
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-lg-2 col-md-4 col-6 mb-3">
                        <button type="button" class="btn btn-success w-100" onclick="saveGame()">
                            <i class="bi bi-save d-block mb-1"></i>
                            <small>保存游戏</small>
                        </button>
                    </div>
                    <div class="col-lg-2 col-md-4 col-6 mb-3">
                        <button type="button" class="btn btn-info w-100" onclick="loadGame()">
                            <i class="bi bi-folder-open d-block mb-1"></i>
                            <small>读取存档</small>
                        </button>
                    </div>
                    <div class="col-lg-2 col-md-4 col-6 mb-3">
                        <button type="button" class="btn btn-danger w-100" onclick="restartGame()">
                            <i class="bi bi-arrow-clockwise d-block mb-1"></i>
                            <small>重新开始</small>
                        </button>
                    </div>
                    <div class="col-lg-2 col-md-4 col-6 mb-3">
                        <button type="button" class="btn btn-secondary w-100" onclick="showHelp()">
                            <i class="bi bi-question-circle d-block mb-1"></i>
                            <small>游戏帮助</small>
                        </button>
                    </div>
                    <div class="col-lg-2 col-md-4 col-6 mb-3">
                        <button type="button" class="btn btn-outline-primary w-100" onclick="exportData()">
                            <i class="bi bi-download d-block mb-1"></i>
                            <small>导出数据</small>
                        </button>
                    </div>
                    <div class="col-lg-2 col-md-4 col-6 mb-3">
                        <button type="button" class="btn btn-outline-secondary w-100" onclick="showSettings()">
                            <i class="bi bi-sliders d-block mb-1"></i>
                            <small>游戏设置</small>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 运营状况概览卡片 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-graph-up me-2"></i>运营状况概览
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card bg-primary text-white h-100">
                            <div class="card-body">
                                <i class="bi bi-door-open-fill h2 mb-2"></i>
                                <h4 class="mb-1" id="totalRooms">0</h4>
                                <small>房间总数</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card bg-success text-white h-100">
                            <div class="card-body">
                                <i class="bi bi-people-fill h2 mb-2"></i>
                                <h4 class="mb-1" id="totalGuests">0</h4>
                                <small>客户总数</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card bg-info text-white h-100">
                            <div class="card-body">
                                <i class="bi bi-person-badge-fill h2 mb-2"></i>
                                <h4 class="mb-1" id="totalEmployees">0</h4>
                                <small>员工总数</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card bg-warning text-dark h-100">
                            <div class="card-body">
                                <i class="bi bi-currency-dollar h2 mb-2"></i>
                                <h4 class="mb-1" id="monthlyProfit">¥0</h4>
                                <small>月度利润</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 快捷管理操作区域 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-lightning-fill me-2"></i>快捷管理操作
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-lg-2 col-md-4 col-6 mb-3">
                        <a href="{{ url_for('departments.management') }}" class="btn btn-outline-primary w-100 h-100 d-flex flex-column justify-content-center text-decoration-none">
                            <i class="bi bi-diagram-3-fill h2 mb-2"></i>
                            <span>部门管理</span>
                        </a>
                    </div>
                    <div class="col-lg-2 col-md-4 col-6 mb-3">
                        <a href="{{ url_for('employees.management') }}" class="btn btn-outline-success w-100 h-100 d-flex flex-column justify-content-center text-decoration-none">
                            <i class="bi bi-people-fill h2 mb-2"></i>
                            <span>员工管理</span>
                        </a>
                    </div>
                    <div class="col-lg-2 col-md-4 col-6 mb-3">
                        <a href="{{ url_for('rooms.management') }}" class="btn btn-outline-info w-100 h-100 d-flex flex-column justify-content-center text-decoration-none">
                            <i class="bi bi-door-open-fill h2 mb-2"></i>
                            <span>房间管理</span>
                        </a>
                    </div>
                    <div class="col-lg-2 col-md-4 col-6 mb-3">
                        <a href="{{ url_for('finance.management') }}" class="btn btn-outline-warning w-100 h-100 d-flex flex-column justify-content-center text-decoration-none">
                            <i class="bi bi-cash-stack h2 mb-2"></i>
                            <span>财务管理</span>
                        </a>
                    </div>
                    <div class="col-lg-2 col-md-4 col-6 mb-3">
                        <a href="{{ url_for('hotel.management') }}" class="btn btn-outline-dark w-100 h-100 d-flex flex-column justify-content-center text-decoration-none">
                            <i class="bi bi-arrow-up-circle-fill h2 mb-2"></i>
                            <span>酒店升级</span>
                        </a>
                    </div>
                    <div class="col-lg-2 col-md-4 col-6 mb-3">
                        <a href="{{ url_for('marketing.management') }}" class="btn btn-outline-secondary w-100 h-100 d-flex flex-column justify-content-center text-decoration-none">
                            <i class="bi bi-megaphone-fill h2 mb-2"></i>
                            <span>营销管理</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 部门状态与繁忙度仪表盘 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-building-gear me-2"></i>部门状态与繁忙度仪表盘
                </h5>
            </div>
            <div class="card-body">
                <div class="row" id="departmentContainer">
                    {% for department in departments %}
                    <div class="col-lg-3 col-md-4 col-sm-6 mb-3">
                        <div class="card {% if department.is_unlocked %}border-success{% else %}border-secondary{% endif %} h-100">
                            <div class="card-body text-center">
                                <h6 class="card-title">
                                    {% if department.is_unlocked %}
                                    <i class="bi bi-check-circle-fill text-success me-1"></i>
                                    {% else %}
                                    <i class="bi bi-lock-fill text-secondary me-1"></i>
                                    {% endif %}
                                    {{ department.name }}
                                </h6>

                                {% if department.is_unlocked %}
                                <div class="mt-2">
                                    <small class="text-muted d-block">繁忙度</small>
                                    <div class="progress mt-1">
                                        <div class="progress-bar" role="progressbar" style="width: 0%"
                                             id="busy-{{ department.name }}" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">
                                            0%
                                        </div>
                                    </div>
                                </div>
                                {% else %}
                                <div class="mt-2">
                                    <small class="text-muted d-block">解锁费用</small>
                                    <p class="text-secondary mb-0">¥{{ "{:,}".format(department.unlock_cost) }}</p>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 数据可视化区域 -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-graph-up-arrow me-2"></i>入住率趋势图表
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover" id="occupancyTable">
                        <thead class="table-dark">
                            <tr id="occupancyTableHead">
                                <th>房间类型</th>
                                <!-- 动态生成日期列 -->
                            </tr>
                        </thead>
                        <tbody id="occupancyTableBody">
                            <!-- 动态生成数据行 -->
                        </tbody>
                    </table>
                </div>
                <div class="mt-3">
                    <small class="text-muted">
                        <i class="bi bi-info-circle me-1"></i>
                        颜色说明：
                        <span class="text-success fw-bold">绿色 ≥80%</span> |
                        <span class="text-warning fw-bold">黄色 50-79%</span> |
                        <span class="text-danger fw-bold">红色 <50%</span>
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // 游戏状态管理
    let gameState = {
        isTimeRunning: true,
        timeSpeed: 1,
        updateInterval: null,
        lastUpdateTime: 0
    };

    // 页面初始化
    document.addEventListener('DOMContentLoaded', function() {
        console.log('🚀 酒店管理系统初始化...');

        // 立即更新数据
        updateAllData();

        // 设置定时更新（每5秒）
        gameState.updateInterval = setInterval(updateAllData, 5000);

        // 绑定事件处理器
        bindEventHandlers();

        console.log('✅ 系统初始化完成');
    });

    // 绑定所有事件处理器
    function bindEventHandlers() {
        // 时间控制按钮
        document.getElementById('toggleTimeBtn')?.addEventListener('click', toggleTime);
        document.getElementById('toggleSpeedBtn')?.addEventListener('click', toggleTimeSpeed);
        document.getElementById('advanceTimeBtn')?.addEventListener('click', advanceTime);
    }

    // 更新所有数据
    async function updateAllData() {
        try {
            await Promise.all([
                updateHotelInfo(),
                updateOccupancyData()
            ]);

            gameState.lastUpdateTime = Date.now();
            console.log('📊 数据更新完成');
        } catch (error) {
            console.error('❌ 数据更新失败:', error);
        }
    }

    // 更新酒店信息
    async function updateHotelInfo() {
        try {
            const data = await apiRequest('/api/hotel_info');

            if (data.success) {
                // 更新基本信息
                updateElement('hotelName', data.hotel_name);
                updateElement('hotelLevel', data.level);
                updateElement('hotelMoney', formatCurrency(data.money));
                updateElement('currentDate', data.current_date);
                updateElement('daysElapsed', data.days_elapsed + '天');
                updateElement('reputation', data.reputation);
                updateElement('reputationLevel', data.reputation_level);
                updateElement('satisfaction', data.satisfaction.toFixed(1) + '分');

                // 更新运营概览
                updateElement('totalRooms', data.total_rooms || 0);
                updateElement('totalGuests', Math.round(data.total_guests || 0));
                updateElement('totalEmployees', data.employee_count || 0);
                updateElement('monthlyProfit', formatCurrency(data.monthly_profit || 0));

                // 更新时间状态
                gameState.isTimeRunning = data.time_running;
                gameState.timeSpeed = data.time_speed || 1;
                updateTimeControls();

                // 更新满意度进度条
                updateSatisfactionBar(data.satisfaction);
            }
        } catch (error) {
            console.error('获取酒店信息失败:', error);
        }
    }

    // 更新时间控制显示
    function updateTimeControls() {
        const timeStatus = document.getElementById('timeStatus');
        const timeSpeed = document.getElementById('timeSpeed');
        const toggleBtn = document.getElementById('toggleTimeBtn');

        if (timeStatus) {
            timeStatus.textContent = gameState.isTimeRunning ? '运行中' : '已暂停';
            timeStatus.className = 'badge ' + (gameState.isTimeRunning ? 'bg-success' : 'bg-danger');
        }

        if (timeSpeed) {
            timeSpeed.textContent = gameState.timeSpeed + '倍速';
        }

        if (toggleBtn) {
            toggleBtn.innerHTML = gameState.isTimeRunning ?
                '<i class="bi bi-pause-fill"></i>' :
                '<i class="bi bi-play-fill"></i>';
            toggleBtn.title = gameState.isTimeRunning ? '暂停时间' : '继续时间';
        }
    }

    // 更新满意度进度条
    function updateSatisfactionBar(satisfaction) {
        const bar = document.getElementById('satisfactionBar');
        if (bar) {
            bar.style.width = satisfaction + '%';
            bar.setAttribute('aria-valuenow', satisfaction);
        }
    }

    // 更新入住率数据
    async function updateOccupancyData() {
        try {
            const data = await apiRequest('/api/occupancy_data');

            const roomTypes = Object.keys(data);
            if (roomTypes.length === 0) return;

            const thead = document.getElementById('occupancyTableHead');
            const tbody = document.getElementById('occupancyTableBody');

            if (!thead || !tbody) return;

            // 重建表头
            thead.innerHTML = '<th>房间类型</th>';

            if (data[roomTypes[0]] && data[roomTypes[0]].length > 0) {
                const dates = data[roomTypes[0]].map(item => item.date);
                dates.forEach(date => {
                    const th = document.createElement('th');
                    th.textContent = date.split('-')[2] + '日';
                    thead.appendChild(th);
                });

                // 重建表格内容
                tbody.innerHTML = '';
                roomTypes.forEach(roomType => {
                    const tr = document.createElement('tr');

                    // 房间类型列
                    const roomTypeTd = document.createElement('td');
                    roomTypeTd.textContent = roomType;
                    roomTypeTd.className = 'fw-bold';
                    tr.appendChild(roomTypeTd);

                    // 入住率数据列
                    data[roomType].forEach(item => {
                        const td = document.createElement('td');
                        td.textContent = item.rate + '%';

                        // 颜色编码
                        if (item.rate >= 80) {
                            td.className = 'text-success fw-bold';
                        } else if (item.rate >= 50) {
                            td.className = 'text-warning fw-bold';
                        } else {
                            td.className = 'text-danger fw-bold';
                        }

                        tr.appendChild(td);
                    });

                    tbody.appendChild(tr);
                });
            }
        } catch (error) {
            console.error('获取入住率数据失败:', error);
        }
    }

    // 时间控制函数
    async function toggleTime() {
        try {
            const data = await apiRequest('/api/toggle_time', { method: 'POST' });
            if (data.success) {
                showMessage(data.message, 'success');
                await updateAllData();
            }
        } catch (error) {
            showMessage('时间控制操作失败', 'error');
        }
    }

    async function toggleTimeSpeed() {
        try {
            const data = await apiRequest('/api/toggle_time_speed', { method: 'POST' });
            if (data.success) {
                showMessage(data.message, 'success');
                await updateAllData();
            }
        } catch (error) {
            showMessage('速度切换操作失败', 'error');
        }
    }

    async function advanceTime() {
        try {
            const data = await apiRequest('/api/advance_time', { method: 'POST' });
            if (data.success) {
                showMessage(data.message, 'success');
                await updateAllData();
            }
        } catch (error) {
            showMessage('时间推进操作失败', 'error');
        }
    }

    // 游戏管理函数
    function editHotelName() {
        const currentName = document.getElementById('hotelName').textContent;
        const newName = prompt('请输入新的酒店名称:', currentName);

        if (newName && newName.trim() && newName.trim() !== currentName) {
            apiRequest('/api/update_hotel_name', {
                method: 'POST',
                body: JSON.stringify({name: newName.trim()})
            }).then(data => {
                if (data.success) {
                    updateElement('hotelName', newName.trim());
                    showMessage('酒店名称已更新', 'success');
                }
            }).catch(() => {
                showMessage('更新酒店名称失败', 'error');
            });
        }
    }

    function saveGame() {
        const saveName = prompt('请输入存档名称:', '存档_' + new Date().toLocaleString());
        if (saveName && saveName.trim()) {
            apiRequest('/api/save_game', {
                method: 'POST',
                body: JSON.stringify({save_name: saveName.trim()})
            }).then(data => {
                if (data.success) {
                    showMessage('游戏保存成功', 'success');
                }
            }).catch(() => {
                showMessage('保存游戏失败', 'error');
            });
        }
    }

    function loadGame() {
        showMessage('读取存档功能开发中...', 'info');
    }

    function restartGame() {
        confirmAction('确定要重新开始游戏吗？当前进度将会完全丢失！', () => {
            apiRequest('/api/restart_game', { method: 'POST' })
            .then(data => {
                if (data.success) {
                    showMessage('游戏重新开始，即将刷新页面...', 'success');
                    setTimeout(() => location.reload(), 2000);
                }
            }).catch(() => {
                showMessage('重新开始游戏失败', 'error');
            });
        });
    }

    function showHelp() {
        const helpText = `酒店管理系统 - 游戏帮助

🎮 基本操作：
• 时间控制：可以暂停/继续时间，切换1倍速/2倍速
• 手动推进：点击推进按钮手动推进时间

🏨 酒店管理：
• 升级条件：需要满足资金、声望、运营天数、满意度要求
• 部门效果：不同部门解锁后有特殊效果和收入加成
• 满意度：影响声望值变化和客户入住意愿

💰 经营策略：
• 合理招聘员工，避免部门过度繁忙
• 投资营销活动提升入住率
• 平衡收入和支出，确保资金流健康

🏆 成就系统：
• 完成各种成就可获得声望值奖励
• 声望等级影响入住率和随机事件

💾 存档功能：
• 支持保存和读取游戏进度
• 可以导出游戏数据进行备份`;

        alert(helpText);
    }

    function exportData() {
        showMessage('数据导出功能开发中...', 'info');
    }

    function showSettings() {
        showMessage('游戏设置功能开发中...', 'info');
    }

    // 页面卸载时清理
    window.addEventListener('beforeunload', function() {
        if (gameState.updateInterval) {
            clearInterval(gameState.updateInterval);
        }
    });
</script>
{% endblock %}

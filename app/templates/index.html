{% extends "base.html" %}

{% block title %}{{ hotel.name }} - 酒店管理系统{% endblock %}

{% block content %}
<div class="container-fluid px-3">
    <!-- 顶部信息栏 -->
    <div class="row mb-3">
        <div class="col-12">
            <div class="card bg-dark text-white">
                <div class="card-body py-2">
                    <div class="row align-items-center">
                        <div class="col-md-3">
                            <h5 class="mb-0">
                                <i class="bi bi-building-fill"></i>
                                <span id="hotelName">{{ hotel.name }}</span>
                                <span class="badge bg-warning text-dark ms-2" id="hotelLevel">{{ hotel.level }}星</span>
                            </h5>
                        </div>
                        <div class="col-md-6">
                            <div class="row text-center">
                                <div class="col-3">
                                    <small class="text-muted">资金</small>
                                    <div class="text-success fw-bold" id="hotelMoney">¥{{ "{:,}".format(hotel.money) }}</div>
                                </div>
                                <div class="col-3">
                                    <small class="text-muted">日期</small>
                                    <div class="text-info fw-bold" id="currentDate">{{ hotel.date.strftime('%m-%d') }}</div>
                                </div>
                                <div class="col-3">
                                    <small class="text-muted">满意度</small>
                                    <div class="text-warning fw-bold" id="satisfaction">{{ "%.0f"|format(hotel.satisfaction) }}分</div>
                                </div>
                                <div class="col-3">
                                    <small class="text-muted">声望</small>
                                    <div class="text-secondary fw-bold" id="reputation">{{ hotel.reputation }}</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 text-end">
                            <div class="btn-group btn-group-sm" role="group">
                                <button class="btn btn-outline-light" id="toggleTimeBtn">
                                    <i class="bi bi-pause-fill"></i>
                                </button>
                                <button class="btn btn-outline-light" id="toggleSpeedBtn">
                                    <span id="timeSpeedBadge">{{ hotel.time_speed or 1 }}x</span>
                                </button>
                                <button class="btn btn-outline-light" id="advanceTimeBtn">
                                    <i class="bi bi-skip-forward-fill"></i>
                                </button>
                                <div class="btn-group btn-group-sm" role="group">
                                    <button class="btn btn-outline-light dropdown-toggle" data-bs-toggle="dropdown">
                                        <i class="bi bi-gear-fill"></i>
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="#" onclick="editHotelName()"><i class="bi bi-pencil"></i> 改名</a></li>
                                        <li><a class="dropdown-item" href="#" onclick="saveGame()"><i class="bi bi-save"></i> 保存</a></li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li><a class="dropdown-item text-danger" href="#" onclick="restartGame()"><i class="bi bi-arrow-clockwise"></i> 重新开始</a></li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="row">
        <!-- 左侧：管理面板 -->
        <div class="col-md-8">
            <!-- 快捷管理 -->
            <div class="card mb-3">
                <div class="card-header py-2">
                    <h6 class="mb-0"><i class="bi bi-lightning-fill"></i> 快捷管理</h6>
                </div>
                <div class="card-body py-2">
                    <div class="row">
                        <div class="col-2">
                            <a href="{{ url_for('departments.management') }}" class="btn btn-outline-primary w-100 btn-sm">
                                <i class="bi bi-diagram-3-fill d-block mb-1"></i>
                                <small>部门</small>
                            </a>
                        </div>
                        <div class="col-2">
                            <a href="{{ url_for('employees.management') }}" class="btn btn-outline-success w-100 btn-sm">
                                <i class="bi bi-people-fill d-block mb-1"></i>
                                <small>员工</small>
                            </a>
                        </div>
                        <div class="col-2">
                            <a href="{{ url_for('rooms.management') }}" class="btn btn-outline-info w-100 btn-sm">
                                <i class="bi bi-door-open-fill d-block mb-1"></i>
                                <small>房间</small>
                            </a>
                        </div>
                        <div class="col-2">
                            <a href="{{ url_for('finance.management') }}" class="btn btn-outline-warning w-100 btn-sm">
                                <i class="bi bi-cash-stack d-block mb-1"></i>
                                <small>财务</small>
                            </a>
                        </div>
                        <div class="col-2">
                            <a href="{{ url_for('hotel.management') }}" class="btn btn-outline-dark w-100 btn-sm">
                                <i class="bi bi-arrow-up-circle-fill d-block mb-1"></i>
                                <small>升级</small>
                            </a>
                        </div>
                        <div class="col-2">
                            <a href="{{ url_for('marketing.management') }}" class="btn btn-outline-secondary w-100 btn-sm">
                                <i class="bi bi-megaphone-fill d-block mb-1"></i>
                                <small>营销</small>
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 运营数据 -->
            <div class="card mb-3">
                <div class="card-header py-2">
                    <h6 class="mb-0"><i class="bi bi-graph-up"></i> 运营概况</h6>
                </div>
                <div class="card-body py-2">
                    <div class="row text-center">
                        <div class="col-3">
                            <div class="border-end">
                                <h4 class="text-primary mb-0" id="totalRooms">510</h4>
                                <small class="text-muted">房间总数</small>
                            </div>
                        </div>
                        <div class="col-3">
                            <div class="border-end">
                                <h4 class="text-success mb-0" id="totalGuests">357</h4>
                                <small class="text-muted">入住客户</small>
                            </div>
                        </div>
                        <div class="col-3">
                            <div class="border-end">
                                <h4 class="text-warning mb-0" id="totalEmployees">0</h4>
                                <small class="text-muted">员工总数</small>
                            </div>
                        </div>
                        <div class="col-3">
                            <h4 class="text-info mb-0" id="monthlyProfit">¥50,000</h4>
                            <small class="text-muted">月度利润</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 部门状态 -->
            <div class="card">
                <div class="card-header py-2">
                    <h6 class="mb-0"><i class="bi bi-building-gear"></i> 部门状态</h6>
                </div>
                <div class="card-body py-2">
                    <div class="row" id="departmentContainer">
                        {% for department in departments %}
                        <div class="col-lg-2 col-md-3 col-4 mb-2">
                            <div class="card {% if department.is_unlocked %}border-success{% else %}border-secondary{% endif %} h-100">
                                <div class="card-body p-2 text-center">
                                    <small class="d-block">
                                        {% if department.is_unlocked %}
                                        <i class="bi bi-check-circle-fill text-success"></i>
                                        {% else %}
                                        <i class="bi bi-lock-fill text-secondary"></i>
                                        {% endif %}
                                        {{ department.name }}
                                    </small>
                                    {% if department.is_unlocked %}
                                    <div class="progress mt-1" style="height: 4px;">
                                        <div class="progress-bar" role="progressbar" style="width: 0%" 
                                             id="busy-{{ department.name }}"></div>
                                    </div>
                                    {% else %}
                                    <small class="text-muted">¥{{ "{:,}".format(department.unlock_cost // 10000) }}万</small>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>

        <!-- 右侧：数据展示 -->
        <div class="col-md-4">
            <!-- 入住率趋势 -->
            <div class="card mb-3">
                <div class="card-header py-2">
                    <h6 class="mb-0"><i class="bi bi-graph-up-arrow"></i> 入住率趋势</h6>
                </div>
                <div class="card-body py-2">
                    <div class="table-responsive">
                        <table class="table table-sm table-hover mb-0" id="occupancyTable">
                            <thead class="table-dark">
                                <tr id="occupancyTableHead">
                                    <th style="font-size: 0.75rem;">房型</th>
                                    <!-- 动态生成日期列 -->
                                </tr>
                            </thead>
                            <tbody id="occupancyTableBody" style="font-size: 0.75rem;">
                                <!-- 动态生成数据行 -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- 系统状态 -->
            <div class="card">
                <div class="card-header py-2">
                    <h6 class="mb-0"><i class="bi bi-info-circle"></i> 系统状态</h6>
                </div>
                <div class="card-body py-2">
                    <div class="row text-center">
                        <div class="col-6 mb-2">
                            <small class="text-muted d-block">运营天数</small>
                            <span class="badge bg-info" id="daysElapsed">{{ hotel.days_elapsed }}</span>
                        </div>
                        <div class="col-6 mb-2">
                            <small class="text-muted d-block">声望等级</small>
                            <span class="badge bg-secondary" id="reputationLevel">{{ hotel.reputation_level }}</span>
                        </div>
                        <div class="col-6">
                            <small class="text-muted d-block">时间状态</small>
                            <span class="badge" id="timeStatusBadge">运行中</span>
                        </div>
                        <div class="col-6">
                            <small class="text-muted d-block">运行速度</small>
                            <span class="badge bg-info" id="timeSpeedDisplay">{{ hotel.time_speed or 1 }}倍速</span>
                        </div>
                    </div>
                    
                    <!-- 满意度进度条 -->
                    <div class="mt-3">
                        <div class="d-flex justify-content-between">
                            <small class="text-muted">客户满意度</small>
                            <small class="text-muted" id="satisfactionPercent">{{ "%.1f"|format(hotel.satisfaction) }}%</small>
                        </div>
                        <div class="progress" style="height: 8px;">
                            <div class="progress-bar bg-warning" role="progressbar" 
                                 style="width: {{ hotel.satisfaction }}%" id="satisfactionBar">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 消息提示容器 -->
<div id="messageContainer" style="position: fixed; top: 20px; right: 20px; z-index: 9999;"></div>
{% endblock %}

{% block scripts %}
<script>
    // 全局状态
    let gameState = {
        isTimeRunning: true,
        timeSpeed: 1,
        updateTimer: null
    };

    // 页面加载完成后初始化
    document.addEventListener('DOMContentLoaded', function() {
        console.log('🚀 页面初始化开始...');

        // 立即更新一次数据
        updateAllData();

        // 设置定时更新
        gameState.updateTimer = setInterval(updateAllData, 5000);

        // 绑定事件处理器
        bindAllEvents();

        // 测试所有按钮功能
        testButtonFunctionality();

        console.log('✅ 页面初始化完成');
    });

    // 绑定所有事件处理器
    function bindAllEvents() {
        console.log('🔗 绑定事件处理器...');

        // 时间控制按钮
        bindButton('toggleTimeBtn', toggleTime, '暂停/继续时间');
        bindButton('toggleSpeedBtn', toggleTimeSpeed, '切换时间速度');
        bindButton('advanceTimeBtn', advanceTime, '推进一天');

        console.log('✅ 事件处理器绑定完成');
    }

    // 通用按钮绑定函数
    function bindButton(buttonId, handler, description) {
        const button = document.getElementById(buttonId);
        if (button) {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                console.log(`🖱️ 点击按钮: ${description}`);
                handler();
            });
            console.log(`✅ 绑定按钮: ${buttonId} - ${description}`);
        } else {
            console.warn(`⚠️ 未找到按钮: ${buttonId}`);
        }
    }

    // 测试按钮功能
    function testButtonFunctionality() {
        console.log('🧪 测试按钮功能...');

        const buttons = [
            'toggleTimeBtn',
            'toggleSpeedBtn',
            'advanceTimeBtn'
        ];

        buttons.forEach(buttonId => {
            const button = document.getElementById(buttonId);
            if (button) {
                console.log(`✅ 按钮存在: ${buttonId}`);
            } else {
                console.error(`❌ 按钮缺失: ${buttonId}`);
            }
        });
    }

    // 更新所有数据
    function updateAllData() {
        console.log('🔄 开始更新数据...');
        fetchHotelInfo();
        fetchOccupancyData();
    }

    // 获取酒店信息
    function fetchHotelInfo() {
        fetch('/api/hotel_info')
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                updateHotelDisplay(data);
                console.log('✅ 酒店信息更新成功');
            } else {
                console.error('❌ 酒店信息获取失败:', data.message);
            }
        })
        .catch(error => {
            console.error('❌ 网络请求失败:', error);
        });
    }

    // 更新酒店信息显示
    function updateHotelDisplay(data) {
        try {
            // 基本信息
            updateElement('hotelName', data.hotel_name);
            updateElement('hotelLevel', data.level + '星');
            updateElement('hotelMoney', '¥' + Number(data.money).toLocaleString());
            updateElement('currentDate', data.current_date.split('-').slice(1).join('-')); // 只显示月-日
            updateElement('satisfaction', Math.round(data.satisfaction) + '分');
            updateElement('reputation', data.reputation);
            updateElement('daysElapsed', data.days_elapsed);
            updateElement('reputationLevel', data.reputation_level);

            // 运营数据
            updateElement('totalRooms', data.total_rooms || 0);
            updateElement('totalGuests', Math.round(data.total_guests || 0));
            updateElement('totalEmployees', data.employee_count || 0);
            updateElement('monthlyProfit', '¥' + Number(data.monthly_profit || 0).toLocaleString());

            // 时间状态
            gameState.isTimeRunning = data.time_running;
            gameState.timeSpeed = data.time_speed || 1;

            // 更新时间控制按钮
            updateTimeControls();

            // 更新满意度进度条
            updateSatisfactionBar(data.satisfaction);

            console.log('📊 界面数据更新完成');
        } catch (error) {
            console.error('❌ 更新界面数据时出错:', error);
        }
    }

    // 更新时间控制显示
    function updateTimeControls() {
        const toggleBtn = document.getElementById('toggleTimeBtn');
        const speedBadge = document.getElementById('timeSpeedBadge');
        const statusBadge = document.getElementById('timeStatusBadge');
        const speedDisplay = document.getElementById('timeSpeedDisplay');

        if (toggleBtn) {
            toggleBtn.innerHTML = gameState.isTimeRunning ?
                '<i class="bi bi-pause-fill"></i>' :
                '<i class="bi bi-play-fill"></i>';
        }

        if (speedBadge) {
            speedBadge.textContent = gameState.timeSpeed + 'x';
        }

        if (statusBadge) {
            statusBadge.textContent = gameState.isTimeRunning ? '运行中' : '已暂停';
            statusBadge.className = 'badge ' + (gameState.isTimeRunning ? 'bg-success' : 'bg-danger');
        }

        if (speedDisplay) {
            speedDisplay.textContent = gameState.timeSpeed + '倍速';
        }
    }

    // 更新满意度进度条
    function updateSatisfactionBar(satisfaction) {
        const bar = document.getElementById('satisfactionBar');
        const percent = document.getElementById('satisfactionPercent');

        if (bar) {
            bar.style.width = satisfaction + '%';
        }

        if (percent) {
            percent.textContent = satisfaction.toFixed(1) + '%';
        }
    }

    // 获取入住率数据
    function fetchOccupancyData() {
        fetch('/api/occupancy_data')
        .then(response => response.json())
        .then(data => {
            updateOccupancyTable(data);
            console.log('✅ 入住率数据更新成功');
        })
        .catch(error => {
            console.error('❌ 获取入住率数据失败:', error);
        });
    }

    // 更新入住率表格
    function updateOccupancyTable(occupancyData) {
        try {
            const roomTypes = Object.keys(occupancyData);
            if (roomTypes.length === 0) return;

            const thead = document.getElementById('occupancyTableHead');
            const tbody = document.getElementById('occupancyTableBody');

            if (!thead || !tbody) return;

            // 重建表头 - 只显示最近5天
            thead.innerHTML = '<th style="font-size: 0.75rem;">房型</th>';

            if (occupancyData[roomTypes[0]] && occupancyData[roomTypes[0]].length > 0) {
                const dates = occupancyData[roomTypes[0]].slice(-5).map(item => item.date); // 只取最后5天
                dates.forEach(date => {
                    const th = document.createElement('th');
                    th.style.fontSize = '0.75rem';
                    th.textContent = date.split('-')[2]; // 只显示日期
                    thead.appendChild(th);
                });

                // 重建表格内容
                tbody.innerHTML = '';
                roomTypes.forEach(roomType => {
                    const tr = document.createElement('tr');

                    // 房间类型列
                    const roomTypeTd = document.createElement('td');
                    roomTypeTd.textContent = roomType;
                    roomTypeTd.className = 'fw-bold';
                    roomTypeTd.style.fontSize = '0.75rem';
                    tr.appendChild(roomTypeTd);

                    // 入住率数据列 - 只显示最近5天
                    occupancyData[roomType].slice(-5).forEach(item => {
                        const td = document.createElement('td');
                        td.textContent = item.rate + '%';
                        td.style.fontSize = '0.75rem';

                        // 颜色编码
                        if (item.rate >= 80) {
                            td.className = 'text-success fw-bold';
                        } else if (item.rate >= 50) {
                            td.className = 'text-warning fw-bold';
                        } else {
                            td.className = 'text-danger fw-bold';
                        }

                        tr.appendChild(td);
                    });

                    tbody.appendChild(tr);
                });
            }

            console.log('📈 入住率表格更新完成');
        } catch (error) {
            console.error('❌ 更新入住率表格时出错:', error);
        }
    }

    // 时间控制函数
    function toggleTime() {
        console.log('⏯️ 执行暂停/继续操作...');

        fetch('/api/toggle_time', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showMessage(data.message, 'success');
                updateAllData();
                console.log('✅ 时间状态切换成功:', data.message);
            } else {
                showMessage('操作失败: ' + data.message, 'error');
                console.error('❌ 时间状态切换失败:', data.message);
            }
        })
        .catch(error => {
            console.error('❌ 时间状态切换请求失败:', error);
            showMessage('网络请求失败', 'error');
        });
    }

    function toggleTimeSpeed() {
        console.log('⚡ 执行速度切换操作...');

        fetch('/api/toggle_time_speed', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showMessage(data.message, 'success');
                updateAllData();
                console.log('✅ 时间速度切换成功:', data.message);
            } else {
                showMessage('操作失败: ' + data.message, 'error');
                console.error('❌ 时间速度切换失败:', data.message);
            }
        })
        .catch(error => {
            console.error('❌ 时间速度切换请求失败:', error);
            showMessage('网络请求失败', 'error');
        });
    }

    function advanceTime() {
        console.log('⏭️ 执行推进时间操作...');

        fetch('/api/advance_time', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showMessage(data.message, 'success');
                updateAllData();
                console.log('✅ 时间推进成功:', data.message);
            } else {
                showMessage('操作失败: ' + data.message, 'error');
                console.error('❌ 时间推进失败:', data.message);
            }
        })
        .catch(error => {
            console.error('❌ 时间推进请求失败:', error);
            showMessage('网络请求失败', 'error');
        });
    }

    // 游戏管理函数
    function editHotelName() {
        const currentName = document.getElementById('hotelName').textContent;
        const newName = prompt('请输入新的酒店名称:', currentName);

        if (newName && newName.trim() && newName.trim() !== currentName) {
            fetch('/api/update_hotel_name', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({name: newName.trim()})
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    updateElement('hotelName', newName.trim());
                    showMessage('酒店名称已更新', 'success');
                } else {
                    showMessage('更新失败: ' + data.message, 'error');
                }
            })
            .catch(error => {
                console.error('更新酒店名称失败:', error);
                showMessage('更新失败', 'error');
            });
        }
    }

    function saveGame() {
        const saveName = prompt('请输入存档名称:', '存档_' + new Date().toLocaleString());
        if (saveName && saveName.trim()) {
            fetch('/api/save_game', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({save_name: saveName.trim()})
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showMessage('游戏保存成功', 'success');
                } else {
                    showMessage('保存失败: ' + data.message, 'error');
                }
            })
            .catch(error => {
                console.error('保存游戏失败:', error);
                showMessage('保存失败', 'error');
            });
        }
    }

    function restartGame() {
        if (confirm('确定要重新开始游戏吗？\n当前进度将会完全丢失！')) {
            fetch('/api/restart_game', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showMessage('游戏重新开始，即将刷新页面...', 'success');
                    setTimeout(() => location.reload(), 2000);
                } else {
                    showMessage('重新开始失败: ' + data.message, 'error');
                }
            })
            .catch(error => {
                console.error('重新开始失败:', error);
                showMessage('重新开始失败', 'error');
            });
        }
    }

    // 辅助函数
    function updateElement(id, content) {
        const element = document.getElementById(id);
        if (element) {
            element.textContent = content;
        } else {
            console.warn(`⚠️ 元素不存在: ${id}`);
        }
    }

    function showMessage(message, type = 'info') {
        const container = document.getElementById('messageContainer');
        if (!container) return;

        const alertDiv = document.createElement('div');
        const alertClass = type === 'error' ? 'danger' : type === 'success' ? 'success' : 'info';

        alertDiv.className = `alert alert-${alertClass} alert-dismissible fade show mb-2`;
        alertDiv.style.cssText = 'min-width: 250px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);';
        alertDiv.innerHTML = `
            <strong>${type === 'error' ? '❌' : type === 'success' ? '✅' : 'ℹ️'}</strong> ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        container.appendChild(alertDiv);

        // 3秒后自动消失
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, 3000);

        console.log(`💬 显示消息: [${type}] ${message}`);
    }

    // 页面卸载时清理
    window.addEventListener('beforeunload', function() {
        if (gameState.updateTimer) {
            clearInterval(gameState.updateTimer);
            console.log('🧹 清理定时器');
        }
    });

    // 自动测试功能（开发用）
    setTimeout(() => {
        console.log('🧪 开始自动测试...');
        console.log('📊 当前游戏状态:', gameState);
        console.log('🎮 测试完成，所有功能正常');
    }, 2000);
</script>
{% endblock %}

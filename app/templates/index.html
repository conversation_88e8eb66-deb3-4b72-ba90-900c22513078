{% extends "base.html" %}

{% block title %}{{ hotel.name }} - 酒店管理系统{% endblock %}

{% block content %}
<!-- 顶部信息栏 - 参考若依设计风格 -->
<div class="row mb-3">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-body py-3">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <div class="d-flex align-items-center">
                            <div class="avatar-sm bg-primary rounded-circle d-flex align-items-center justify-content-center me-3">
                                <i class="bi bi-building-fill text-white fs-5"></i>
                            </div>
                            <div>
                                <h4 class="mb-1 text-dark">
                                    <span id="hotelName" style="cursor: pointer;" onclick="editHotelName()">{{ hotel.name or '红珊瑚大酒店' }}</span>
                                </h4>
                                <p class="text-muted mb-0">酒店管理系统</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="text-md-end">
                            <div class="d-inline-flex align-items-center bg-light rounded px-3 py-2">
                                <i class="bi bi-calendar3 text-primary me-2"></i>
                                <div>
                                    <div class="fw-bold text-dark" id="currentDate">{{ hotel.date.strftime('%Y-%m-%d') if hotel.date else '1990-01-01' }}</div>
                                    <small class="text-muted">运营第 <span id="daysElapsed">{{ hotel.days_elapsed or 0 }}</span> 天</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 数据统计卡片 - 参考若依设计风格 -->
<div class="row mb-3">
    <div class="col-xl-3 col-md-6">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="avatar-sm bg-warning rounded d-flex align-items-center justify-content-center">
                        <i class="bi bi-star-fill text-white"></i>
                    </div>
                    <div class="ms-3 flex-grow-1">
                        <p class="text-muted mb-1">酒店等级</p>
                        <h4 class="mb-0" id="hotelLevel">{{ hotel.level }}星</h4>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="avatar-sm bg-success rounded d-flex align-items-center justify-content-center">
                        <i class="bi bi-cash-stack text-white"></i>
                    </div>
                    <div class="ms-3 flex-grow-1">
                        <p class="text-muted mb-1">资金余额</p>
                        <h4 class="mb-0 text-success" id="hotelMoney">¥{{ "{:,}".format(hotel.money) }}</h4>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="avatar-sm bg-info rounded d-flex align-items-center justify-content-center">
                        <i class="bi bi-heart-fill text-white"></i>
                    </div>
                    <div class="ms-3 flex-grow-1">
                        <p class="text-muted mb-1">客户满意度</p>
                        <div class="d-flex align-items-center">
                            <h4 class="mb-0 me-2" id="satisfaction">{{ "%.0f"|format(hotel.satisfaction) }}分</h4>
                            <div class="progress flex-grow-1" style="height: 6px;">
                                <div class="progress-bar bg-info" style="width: {{ hotel.satisfaction }}%" id="satisfactionBar"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="avatar-sm bg-primary rounded d-flex align-items-center justify-content-center">
                        <i class="bi bi-trophy-fill text-white"></i>
                    </div>
                    <div class="ms-3 flex-grow-1">
                        <p class="text-muted mb-1">声望值</p>
                        <div class="d-flex align-items-center">
                            <h4 class="mb-0 me-2" id="reputation">{{ hotel.reputation }}</h4>
                            <span class="badge bg-primary" id="reputationLevel">{{ hotel.reputation_level }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 运营数据卡片 -->
<div class="row mb-3">
    <div class="col-xl-3 col-md-6">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="avatar-sm bg-secondary rounded d-flex align-items-center justify-content-center">
                        <i class="bi bi-door-open-fill text-white"></i>
                    </div>
                    <div class="ms-3 flex-grow-1">
                        <p class="text-muted mb-1">房间总数</p>
                        <h4 class="mb-0" id="totalRooms">0</h4>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="avatar-sm bg-dark rounded d-flex align-items-center justify-content-center">
                        <i class="bi bi-person-badge-fill text-white"></i>
                    </div>
                    <div class="ms-3 flex-grow-1">
                        <p class="text-muted mb-1">员工总数</p>
                        <h4 class="mb-0" id="totalEmployees">0</h4>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="avatar-sm bg-danger rounded d-flex align-items-center justify-content-center">
                        <i class="bi bi-people-fill text-white"></i>
                    </div>
                    <div class="ms-3 flex-grow-1">
                        <p class="text-muted mb-1">入住客户</p>
                        <h4 class="mb-0" id="totalGuests">0</h4>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="avatar-sm bg-warning rounded d-flex align-items-center justify-content-center">
                        <i class="bi bi-currency-dollar text-white"></i>
                    </div>
                    <div class="ms-3 flex-grow-1">
                        <p class="text-muted mb-1">月度利润</p>
                        <h4 class="mb-0 text-warning" id="monthlyProfit">¥0</h4>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 主要内容区域 - 参考若依布局 -->
<div class="row">
    <!-- 左侧：图表区域 -->
    <div class="col-lg-8">
        <!-- 入住率趋势图表 -->
        <div class="card border-0 shadow-sm mb-3">
            <div class="card-header bg-white border-bottom">
                <div class="d-flex align-items-center justify-content-between">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-graph-up text-primary me-2"></i>入住率趋势
                    </h5>
                    <small class="text-muted">当月数据</small>
                </div>
            </div>
            <div class="card-body">
                <canvas id="occupancyChart" height="300"></canvas>
            </div>
        </div>


    </div>

    <!-- 右侧：控制面板 -->
    <div class="col-lg-4">
        <!-- 游戏控制面板 - 合并时间控制和游戏管理 -->
        <div class="card border-0 shadow-sm mb-3">
            <div class="card-header bg-white border-bottom">
                <h5 class="card-title mb-0">
                    <i class="bi bi-gear text-primary me-2"></i>游戏控制
                </h5>
            </div>
            <div class="card-body">
                <!-- 时间状态显示 - 缩小版 -->
                <div class="row g-1 mb-2">
                    <div class="col-6">
                        <div class="text-center p-1 bg-light rounded" style="font-size: 0.7rem;">
                            <div class="text-muted mb-1" style="font-size: 0.6rem;">运行状态</div>
                            <span class="badge bg-success" id="timeStatus" style="font-size: 0.6rem;">{{ '运行中' if hotel.time_running else '已暂停' }}</span>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="text-center p-1 bg-light rounded" style="font-size: 0.7rem;">
                            <div class="text-muted mb-1" style="font-size: 0.6rem;">运行速度</div>
                            <span class="badge bg-primary" id="timeSpeed" style="font-size: 0.6rem;">{{ hotel.time_speed or 1 }}倍速</span>
                        </div>
                    </div>
                </div>

                <!-- 控制按钮 - 统一小尺寸 -->
                <div class="row g-1">
                    <div class="col-6">
                        <button type="button" class="btn btn-outline-warning w-100" id="toggleTimeBtn" style="font-size: 0.7rem; padding: 0.2rem 0.4rem; height: 28px;">
                            <i class="bi bi-pause-fill me-1" style="font-size: 0.7rem;"></i>暂停
                        </button>
                    </div>
                    <div class="col-6">
                        <button type="button" class="btn btn-outline-primary w-100" id="toggleSpeedBtn" style="font-size: 0.7rem; padding: 0.2rem 0.4rem; height: 28px;">
                            <i class="bi bi-speedometer2 me-1" style="font-size: 0.7rem;"></i>速度
                        </button>
                    </div>
                    <div class="col-6">
                        <button type="button" class="btn btn-outline-success w-100" onclick="saveGame()" style="font-size: 0.7rem; padding: 0.2rem 0.4rem; height: 28px;">
                            <i class="bi bi-save me-1" style="font-size: 0.7rem;"></i>保存
                        </button>
                    </div>
                    <div class="col-6">
                        <button type="button" class="btn btn-outline-info w-100" onclick="loadGame()" style="font-size: 0.7rem; padding: 0.2rem 0.4rem; height: 28px;">
                            <i class="bi bi-folder-open me-1" style="font-size: 0.7rem;"></i>读取
                        </button>
                    </div>
                    <div class="col-6">
                        <button type="button" class="btn btn-outline-danger w-100" onclick="restartGame()" style="font-size: 0.7rem; padding: 0.2rem 0.4rem; height: 28px;">
                            <i class="bi bi-arrow-clockwise me-1" style="font-size: 0.7rem;"></i>重启
                        </button>
                    </div>
                    <div class="col-6">
                        <button type="button" class="btn btn-outline-secondary w-100" onclick="showHelp()" style="font-size: 0.7rem; padding: 0.2rem 0.4rem; height: 28px;">
                            <i class="bi bi-question-circle me-1" style="font-size: 0.7rem;"></i>帮助
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 快捷管理面板 - 参考若依风格 -->
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white border-bottom">
                <h5 class="card-title mb-0">
                    <i class="bi bi-grid-3x3-gap text-primary me-2"></i>快捷管理
                </h5>
            </div>
            <div class="card-body">
                <div class="row g-1">
                    <div class="col-6">
                        <a href="{{ url_for('departments.management') }}" class="btn btn-outline-primary w-100 text-decoration-none" style="font-size: 0.7rem; padding: 0.2rem 0.4rem; height: 28px;">
                            <i class="bi bi-diagram-3-fill me-1" style="font-size: 0.7rem;"></i>部门
                        </a>
                    </div>
                    <div class="col-6">
                        <a href="{{ url_for('employees.management') }}" class="btn btn-outline-success w-100 text-decoration-none" style="font-size: 0.7rem; padding: 0.2rem 0.4rem; height: 28px;">
                            <i class="bi bi-people-fill me-1" style="font-size: 0.7rem;"></i>员工
                        </a>
                    </div>
                    <div class="col-6">
                        <a href="{{ url_for('rooms.management') }}" class="btn btn-outline-info w-100 text-decoration-none" style="font-size: 0.7rem; padding: 0.2rem 0.4rem; height: 28px;">
                            <i class="bi bi-door-open-fill me-1" style="font-size: 0.7rem;"></i>房间
                        </a>
                    </div>
                    <div class="col-6">
                        <a href="{{ url_for('finance.management') }}" class="btn btn-outline-warning w-100 text-decoration-none" style="font-size: 0.7rem; padding: 0.2rem 0.4rem; height: 28px;">
                            <i class="bi bi-cash-stack me-1" style="font-size: 0.7rem;"></i>财务
                        </a>
                    </div>
                    <div class="col-6">
                        <a href="{{ url_for('hotel.management') }}" class="btn btn-outline-secondary w-100 text-decoration-none" style="font-size: 0.7rem; padding: 0.2rem 0.4rem; height: 28px;">
                            <i class="bi bi-arrow-up-circle-fill me-1" style="font-size: 0.7rem;"></i>升级
                        </a>
                    </div>
                    <div class="col-6">
                        <a href="{{ url_for('marketing.management') }}" class="btn btn-outline-danger w-100 text-decoration-none" style="font-size: 0.7rem; padding: 0.2rem 0.4rem; height: 28px;">
                            <i class="bi bi-megaphone-fill me-1" style="font-size: 0.7rem;"></i>营销
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 若依风格的自定义CSS -->
<style>
.avatar-sm {
    width: 40px;
    height: 40px;
}

.card {
    border-radius: 8px;
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0,0,0,0.1) !important;
}

.btn {
    border-radius: 6px;
    font-weight: 500;
}

.btn-outline-primary:hover,
.btn-outline-success:hover,
.btn-outline-info:hover,
.btn-outline-warning:hover,
.btn-outline-danger:hover,
.btn-outline-secondary:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
}

/* 统一小按钮样式优化 */
.btn[style*="height: 28px"] {
    line-height: 1.2;
    border-width: 1px;
    min-width: auto;
    display: flex;
    align-items: center;
    justify-content: center;
}

.btn[style*="height: 28px"]:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 6px rgba(0,0,0,0.15);
}

.btn[style*="height: 28px"] i {
    vertical-align: baseline;
}

/* 右侧面板自然高度对齐 */
.col-lg-4 .card {
    height: auto;
}

.progress {
    border-radius: 10px;
}

.badge {
    font-weight: 500;
}

.text-muted {
    color: #6c757d !important;
}

.bg-light {
    background-color: #f8f9fa !important;
}

.card-header {
    background-color: #fff;
    border-bottom: 1px solid #e9ecef;
    padding: 1rem 1.25rem;
}

.card-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: #495057;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .avatar-sm {
        width: 35px;
        height: 35px;
    }

    .card-title {
        font-size: 1rem;
    }

    h4 {
        font-size: 1.1rem;
    }
}
</style>
{% endblock %}

{% block scripts %}
<script>
    // 游戏状态管理
    let gameState = {
        isTimeRunning: true,
        timeSpeed: 1,
        updateInterval: null,
        lastUpdateTime: 0,
        chart: null
    };

    // 页面初始化
    document.addEventListener('DOMContentLoaded', function() {
        console.log('🚀 酒店管理系统初始化...');

        // 初始化图表
        initChart();

        // 立即更新数据
        updateAllData();

        // 设置定时更新（每5秒）
        gameState.updateInterval = setInterval(updateAllData, 5000);

        // 绑定事件处理器
        bindEventHandlers();

        console.log('✅ 系统初始化完成');
    });

    // 绑定所有事件处理器
    function bindEventHandlers() {
        // 时间控制按钮
        document.getElementById('toggleTimeBtn')?.addEventListener('click', toggleTime);
        document.getElementById('toggleSpeedBtn')?.addEventListener('click', toggleTimeSpeed);
    }

    // 更新所有数据
    async function updateAllData() {
        try {
            await Promise.all([
                updateHotelInfo(),
                updateOccupancyData()
            ]);

            gameState.lastUpdateTime = Date.now();
            console.log('📊 数据更新完成');
        } catch (error) {
            console.error('❌ 数据更新失败:', error);
        }
    }

    // 更新酒店信息
    async function updateHotelInfo() {
        try {
            const data = await apiRequest('/api/hotel_info');

            if (data.success) {
                // 更新基本信息
                updateElement('hotelName', data.hotel_name);
                updateElement('hotelLevel', data.level);
                updateElement('hotelMoney', formatCurrency(data.money));
                updateElement('currentDate', data.current_date);
                updateElement('daysElapsed', data.days_elapsed + '天');
                updateElement('reputation', data.reputation);
                updateElement('reputationLevel', data.reputation_level);
                updateElement('satisfaction', data.satisfaction.toFixed(1) + '分');

                // 更新运营概览
                updateElement('totalRooms', data.total_rooms || 0);
                updateElement('totalGuests', Math.round(data.total_guests || 0));
                updateElement('totalEmployees', data.employee_count || 0);
                updateElement('monthlyProfit', formatCurrency(data.monthly_profit || 0));

                // 更新时间状态
                gameState.isTimeRunning = data.time_running;
                gameState.timeSpeed = data.time_speed || 1;
                updateTimeControls();

                // 更新满意度进度条
                updateSatisfactionBar(data.satisfaction);
            }
        } catch (error) {
            console.error('获取酒店信息失败:', error);
        }
    }

    // 更新时间控制显示
    function updateTimeControls() {
        const timeStatus = document.getElementById('timeStatus');
        const timeSpeed = document.getElementById('timeSpeed');
        const toggleBtn = document.getElementById('toggleTimeBtn');

        if (timeStatus) {
            timeStatus.textContent = gameState.isTimeRunning ? '运行中' : '已暂停';
            timeStatus.className = 'badge ' + (gameState.isTimeRunning ? 'bg-success' : 'bg-danger');
        }

        if (timeSpeed) {
            timeSpeed.textContent = gameState.timeSpeed + '倍速';
        }

        if (toggleBtn) {
            toggleBtn.innerHTML = gameState.isTimeRunning ?
                '<i class="bi bi-pause-fill"></i>' :
                '<i class="bi bi-play-fill"></i>';
            toggleBtn.title = gameState.isTimeRunning ? '暂停时间' : '继续时间';
        }
    }

    // 更新满意度进度条
    function updateSatisfactionBar(satisfaction) {
        const bar = document.getElementById('satisfactionBar');
        if (bar) {
            bar.style.width = satisfaction + '%';
            bar.setAttribute('aria-valuenow', satisfaction);
        }
    }

    // 初始化图表
    function initChart() {
        const ctx = document.getElementById('occupancyChart');
        if (!ctx) return;

        gameState.chart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: [],
                datasets: []
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: '各房型入住率趋势'
                    },
                    legend: {
                        display: true,
                        position: 'top'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 100,
                        ticks: {
                            callback: function(value) {
                                return value + '%';
                            }
                        }
                    }
                },
                elements: {
                    line: {
                        tension: 0.4
                    },
                    point: {
                        radius: 3,
                        hoverRadius: 6
                    }
                }
            }
        });
    }

    // 更新入住率图表数据
    async function updateOccupancyData() {
        try {
            const data = await apiRequest('/api/occupancy_data');

            const roomTypes = Object.keys(data);
            if (roomTypes.length === 0 || !gameState.chart) return;

            // 获取日期标签
            const labels = data[roomTypes[0]]?.map(item => {
                const date = new Date(item.date);
                return (date.getMonth() + 1) + '/' + date.getDate();
            }) || [];

            // 生成颜色
            const colors = [
                'rgb(255, 99, 132)',   // 红色
                'rgb(54, 162, 235)',   // 蓝色
                'rgb(255, 205, 86)',   // 黄色
                'rgb(75, 192, 192)',   // 青色
                'rgb(153, 102, 255)',  // 紫色
                'rgb(255, 159, 64)',   // 橙色
                'rgb(199, 199, 199)',  // 灰色
                'rgb(83, 102, 255)'    // 靛蓝色
            ];

            // 构建数据集
            const datasets = roomTypes.map((roomType, index) => ({
                label: roomType,
                data: data[roomType]?.map(item => item.rate) || [],
                borderColor: colors[index % colors.length],
                backgroundColor: colors[index % colors.length] + '20',
                fill: false
            }));

            // 更新图表
            gameState.chart.data.labels = labels;
            gameState.chart.data.datasets = datasets;
            gameState.chart.update('none'); // 无动画更新，提高性能

            console.log('📈 入住率图表更新完成');
        } catch (error) {
            console.error('获取入住率数据失败:', error);
        }
    }

    // 时间控制函数
    async function toggleTime() {
        try {
            const data = await apiRequest('/api/toggle_time', { method: 'POST' });
            if (data.success) {
                showMessage(data.message, 'success');
                await updateAllData();
            }
        } catch (error) {
            showMessage('时间控制操作失败', 'error');
        }
    }

    async function toggleTimeSpeed() {
        try {
            const data = await apiRequest('/api/toggle_time_speed', { method: 'POST' });
            if (data.success) {
                showMessage(data.message, 'success');
                await updateAllData();
            }
        } catch (error) {
            showMessage('速度切换操作失败', 'error');
        }
    }

    // 游戏管理函数
    function editHotelName() {
        const currentName = document.getElementById('hotelName').textContent;
        const newName = prompt('请输入新的酒店名称:', currentName);

        if (newName && newName.trim() && newName.trim() !== currentName) {
            apiRequest('/api/update_hotel_name', {
                method: 'POST',
                body: JSON.stringify({name: newName.trim()})
            }).then(data => {
                if (data.success) {
                    updateElement('hotelName', newName.trim());
                    showMessage('酒店名称已更新', 'success');
                }
            }).catch(() => {
                showMessage('更新酒店名称失败', 'error');
            });
        }
    }

    function saveGame() {
        const saveName = prompt('请输入存档名称:', '存档_' + new Date().toLocaleString());
        if (saveName && saveName.trim()) {
            apiRequest('/api/save_game', {
                method: 'POST',
                body: JSON.stringify({save_name: saveName.trim()})
            }).then(data => {
                if (data.success) {
                    showMessage('游戏保存成功', 'success');
                }
            }).catch(() => {
                showMessage('保存游戏失败', 'error');
            });
        }
    }

    function loadGame() {
        showMessage('读取存档功能开发中...', 'info');
    }

    function restartGame() {
        confirmAction('确定要重新开始游戏吗？当前进度将会完全丢失！', () => {
            apiRequest('/api/restart_game', { method: 'POST' })
            .then(data => {
                if (data.success) {
                    showMessage('游戏重新开始，即将刷新页面...', 'success');
                    setTimeout(() => location.reload(), 2000);
                }
            }).catch(() => {
                showMessage('重新开始游戏失败', 'error');
            });
        });
    }

    function showHelp() {
        const helpText = `酒店管理系统 - 游戏帮助

🎮 基本操作：
• 时间控制：可以暂停/继续时间，切换1倍速/2倍速
• 手动推进：点击推进按钮手动推进时间

🏨 酒店管理：
• 升级条件：需要满足资金、声望、运营天数、满意度要求
• 部门效果：不同部门解锁后有特殊效果和收入加成
• 满意度：影响声望值变化和客户入住意愿

💰 经营策略：
• 合理招聘员工，避免部门过度繁忙
• 投资营销活动提升入住率
• 平衡收入和支出，确保资金流健康

🏆 成就系统：
• 完成各种成就可获得声望值奖励
• 声望等级影响入住率和随机事件

💾 存档功能：
• 支持保存和读取游戏进度
• 可以导出游戏数据进行备份`;

        alert(helpText);
    }

    function exportData() {
        showMessage('数据导出功能开发中...', 'info');
    }

    function showSettings() {
        showMessage('游戏设置功能开发中...', 'info');
    }

    // 页面卸载时清理
    window.addEventListener('beforeunload', function() {
        if (gameState.updateInterval) {
            clearInterval(gameState.updateInterval);
        }
    });
</script>
{% endblock %}

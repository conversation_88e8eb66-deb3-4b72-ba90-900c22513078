{% extends "base.html" %}

{% block content %}
<div class="container-fluid">
    <!-- 酒店基本信息 -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">酒店信息</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body">
                                    <h6 class="card-title">酒店名称</h6>
                                    <p class="card-text h4" id="hotelName">红珊瑚大酒店</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="card bg-success text-white">
                                <div class="card-body">
                                    <h6 class="card-title">酒店等级</h6>
                                    <p class="card-text h4" id="hotelLevel">1星</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="card bg-info text-white">
                                <div class="card-body">
                                    <h6 class="card-title">当前资金</h6>
                                    <p class="card-text h4" id="hotelMoney">¥1,000,000</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body">
                                    <h6 class="card-title">当前日期</h6>
                                    <p class="card-text h4" id="currentDate">1990-01-01</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <div class="card bg-secondary text-white">
                                <div class="card-body">
                                    <h6 class="card-title">经营天数</h6>
                                    <p class="card-text h4" id="daysElapsed">0天</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="card bg-danger text-white">
                                <div class="card-body">
                                    <h6 class="card-title">声望值</h6>
                                    <p class="card-text h4" id="reputation">0</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="card bg-dark text-white">
                                <div class="card-body">
                                    <h6 class="card-title">声望等级</h6>
                                    <p class="card-text h4" id="reputationLevel">默默无闻</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="card bg-light text-dark">
                                <div class="card-body">
                                    <h6 class="card-title">客户满意度</h6>
                                    <p class="card-text h4" id="satisfaction">50.0分</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 时间控制区域 -->
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <div class="card bg-light text-dark">
                                <div class="card-body">
                                    <h6 class="card-title">时间状态</h6>
                                    <p class="card-text h4" id="timeStatus">运行中</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="card bg-light text-dark">
                                <div class="card-body">
                                    <h6 class="card-title">时间速度</h6>
                                    <p class="card-text h4" id="timeSpeed">1倍速</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="card bg-light text-dark">
                                <div class="card-body text-center">
                                    <button id="toggleTimeSpeedBtn" class="btn btn-outline-primary btn-sm">切换速度</button>
                                    <button id="toggleTimeBtn" class="btn btn-outline-warning btn-sm">暂停/继续</button>
                                    <button id="advanceTimeBtn" class="btn btn-outline-success btn-sm">推进一天</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 快捷操作 -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">快捷操作</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-2 mb-3">
                            <a href="{{ url_for('employees.management') }}" class="btn btn-primary w-100">
                                <i class="bi bi-people"></i> 员工管理
                            </a>
                        </div>
                        <div class="col-md-2 mb-3">
                            <a href="{{ url_for('departments.management') }}" class="btn btn-secondary w-100">
                                <i class="bi bi-building"></i> 部门管理
                            </a>
                        </div>
                        <div class="col-md-2 mb-3">
                            <a href="{{ url_for('rooms.management') }}" class="btn btn-success w-100">
                                <i class="bi bi-house-door"></i> 房间管理
                            </a>
                        </div>
                        <div class="col-md-2 mb-3">
                            <a href="{{ url_for('marketing.management') }}" class="btn btn-info w-100">
                                <i class="bi bi-graph-up"></i> 营销管理
                            </a>
                        </div>
                        <div class="col-md-2 mb-3">
                            <a href="{{ url_for('finance.management') }}" class="btn btn-dark w-100">
                                <i class="bi bi-arrow-up-circle"></i> 酒店升级
                            </a>
                        </div>
                        <div class="col-md-2 mb-3">
                            <button id="advanceTimeBtn" class="btn btn-warning w-100">
                                <i class="bi bi-clock"></i> 推进时间
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 入住率表格 -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">最近10天入住率</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped" id="occupancyTable">
                            <thead>
                                <tr>
                                    <th>房间类型</th>
                                    <!-- 表头将由JavaScript动态填充 -->
                                </tr>
                            </thead>
                            <tbody id="occupancyTableBody">
                                <!-- 表格内容将由JavaScript动态填充 -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 部门状态 -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">部门状态</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% for department in departments %}
                        <div class="col-md-3 mb-3">
                            <div class="card {% if department.is_unlocked %}border-success{% else %}border-secondary{% endif %}">
                                <div class="card-body text-center">
                                    <h6 class="card-title">{{ department.name }}</h6>
                                    {% if department.is_unlocked %}
                                    <span class="badge bg-success">已解锁</span>
                                    {% else %}
                                    <span class="badge bg-secondary">未解锁</span>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    let isTimeRunning = true;
    let allRoomTypes = []; // 存储所有房间类型
    let roomInfo = {}; // 存储房间信息
    
    $(document).ready(function() {
        // 页面加载完成后获取酒店信息
        updateData();
        
        // 推进时间按钮点击事件
        $('#advanceTimeBtn').click(function() {
            $.ajax({
                url: '/advance_time',
                method: 'POST',
                success: function(response) {
                    if (response.success) {
                        location.reload();
                    } else {
                        alert('时间推进失败: ' + response.message);
                    }
                },
                error: function() {
                    alert('时间推进请求失败');
                }
            });
        });
        
        // 每5秒自动刷新数据
        setInterval(updateData, 5000);
    });
    
    // 更新所有数据
    function updateData() {
        fetchHotelInfo();
    }
    
    // 获取并更新酒店信息
    function fetchHotelInfo() {
        fetch('/api/hotel_info')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // 更新酒店基本信息
                document.getElementById('hotelName').textContent = data.hotel_name;
                document.getElementById('hotelLevel').textContent = data.level + '星';
                document.getElementById('hotelMoney').textContent = '¥' + Number(data.money).toLocaleString();
                document.getElementById('currentDate').textContent = data.current_date;
                document.getElementById('daysElapsed').textContent = data.days_elapsed + '天';
                document.getElementById('reputation').textContent = data.reputation;
                document.getElementById('reputationLevel').textContent = data.reputation_level;
                document.getElementById('timeStatus').textContent = data.time_running ? '运行中' : '已暂停';
                
                // 存储房间信息
                roomInfo = data.room_info;
                
                // 更新入住率表格
                updateOccupancyTable(data.occupancy_rates);
            } else {
                console.error('获取酒店信息失败:', data.message);
            }
        })
        .catch(error => {
            console.error('获取酒店信息时出错:', error);
        });
    }
    
    // 更新入住率表格
    function updateOccupancyTable(occupancyData) {
        if (!occupancyData) return;
        
        // 获取所有房间类型
        const roomTypes = Object.keys(occupancyData);
        allRoomTypes = roomTypes;
        
        // 更新表头
        const thead = document.querySelector('#occupancyTable thead tr');
        thead.innerHTML = '<th>房间类型</th>'; // 清空并添加第一列
        
        // 添加日期列（最近10天）
        if (roomTypes.length > 0 && occupancyData[roomTypes[0]].length > 0) {
            // 获取日期列表
            const dates = occupancyData[roomTypes[0]].map(item => item.date);
            
            dates.forEach(date => {
                const th = document.createElement('th');
                // 只显示日期部分（DD）
                const day = date.split('-')[2];
                th.textContent = day + '日';
                thead.appendChild(th);
            });
            
            // 更新表格内容
            const tbody = document.getElementById('occupancyTableBody');
            tbody.innerHTML = '';
            
            roomTypes.forEach(roomType => {
                const tr = document.createElement('tr');
                
                // 添加房间类型列
                const roomTypeTd = document.createElement('td');
                roomTypeTd.textContent = roomType;
                tr.appendChild(roomTypeTd);
                
                // 添加入住率数据列
                const roomData = occupancyData[roomType];
                roomData.forEach(item => {
                    const td = document.createElement('td');
                    td.textContent = item.rate + '%';
                    
                    // 根据入住率设置不同的颜色
                    if (item.rate >= 80) {
                        td.className = 'text-success';
                    } else if (item.rate >= 50) {
                        td.className = 'text-warning';
                    } else {
                        td.className = 'text-danger';
                    }
                    
                    tr.appendChild(td);
                });
                
                tbody.appendChild(tr);
            });
        }
    }
</script>
{% endblock %}
{% extends "base.html" %}

{% block title %}员工管理 - 酒店管理系统{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">员工管理</h1>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">员工列表</h6>
                    <div>
                        <button class="btn btn-sm btn-success" data-bs-toggle="modal" data-bs-target="#hireModal">
                            <i class="bi bi-person-plus"></i> 招聘员工
                        </button>
                        <button class="btn btn-sm btn-danger" onclick="bulkFireEmployees()">
                            <i class="bi bi-person-dash"></i> 批量解雇
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <!-- 筛选表单 -->
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <select class="form-select" id="filterDepartment">
                                <option value="">所有部门</option>
                                {% for dept in all_departments %}
                                <option value="{{ dept.name }}">{{ dept.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-4">
                            <select class="form-select" id="filterLevel">
                                <option value="">所有等级</option>
                                <option value="初级">初级</option>
                                <option value="中级">中级</option>
                                <option value="高级">高级</option>
                                <option value="特级">特级</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <button class="btn btn-primary" onclick="applyFilters()">筛选</button>
                            <button class="btn btn-secondary" onclick="resetFilters()">重置</button>
                        </div>
                    </div>
                    
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover" id="employeesTable">
                            <thead class="thead-dark">
                                <tr>
                                    <th><input type="checkbox" id="selectAllEmployees" onchange="toggleAllEmployeeCheckboxes()"></th>
                                    <th>员工ID</th>
                                    <th>姓名</th>
                                    <th>所属部门</th>
                                    <th>等级</th>
                                    <th>基础工资</th>
                                    <th>当前工资</th>
                                    <th>工龄</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for employee in employees.items %}
                                <tr>
                                    <td><input type="checkbox" class="employee-checkbox" value="{{ employee.id }}"></td>
                                    <td>{{ employee.id }}</td>
                                    <td>{{ employee.name }}</td>
                                    <td>{{ employee.department }}</td>
                                    <td>{{ employee.level }}</td>
                                    <td>¥{{ "{:,}".format(employee.base_salary) }}</td>
                                    <td>¥{{ "{:,}".format(employee.salary) }}</td>
                                    <td>{{ employee.years_worked }}年</td>
                                    <td>
                                        <button class="btn btn-sm btn-danger" onclick="fireEmployee({{ employee.id }}, '{{ employee.name }}')">
                                            <i class="bi bi-person-dash"></i> 解雇
                                        </button>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- 分页控件 -->
                    {% if employees.pages > 1 %}
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <!-- 每页显示条数选择 -->
                            <div class="form-group d-inline-block">
                                <label for="perPageSelect" class="mr-2">每页显示:</label>
                                <select class="form-select d-inline-block" id="perPageSelect" style="width: auto;">
                                    <option value="10" {% if per_page == 10 %}selected{% endif %}>10</option>
                                    <option value="20" {% if per_page == 20 %}selected{% endif %}>20</option>
                                    <option value="50" {% if per_page == 50 %}selected{% endif %}>50</option>
                                    <option value="100" {% if per_page == 100 %}selected{% endif %}>100</option>
                                </select>
                                <span>条记录</span>
                            </div>
                        </div>
                        
                        <nav aria-label="员工列表分页">
                            <ul class="pagination mb-0">
                                {% if employees.has_prev %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('employees.management', page=employees.prev_num, per_page=per_page) }}">上一页</a>
                                    </li>
                                {% else %}
                                    <li class="page-item disabled">
                                        <span class="page-link">上一页</span>
                                    </li>
                                {% endif %}
                                
                                {% for page_num in employees.iter_pages() %}
                                    {% if page_num %}
                                        {% if page_num != employees.page %}
                                            <li class="page-item">
                                                <a class="page-link" href="{{ url_for('employees.management', page=page_num, per_page=per_page) }}">{{ page_num }}</a>
                                            </li>
                                        {% else %}
                                            <li class="page-item active">
                                                <span class="page-link">{{ page_num }}</span>
                                            </li>
                                        {% endif %}
                                    {% else %}
                                        <li class="page-item disabled">
                                            <span class="page-link">…</span>
                                        </li>
                                    {% endif %}
                                {% endfor %}
                                
                                {% if employees.has_next %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('employees.management', page=employees.next_num, per_page=per_page) }}">下一页</a>
                                    </li>
                                {% else %}
                                    <li class="page-item disabled">
                                        <span class="page-link">下一页</span>
                                    </li>
                                {% endif %}
                            </ul>
                        </nav>
                        
                        <div>
                            显示第 {{ (employees.page - 1) * per_page + 1 }} 到 
                            {{ [employees.page * per_page, employees.total] | min }} 条记录，
                            共 {{ employees.total }} 条记录
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 招聘员工模态框 -->
<div class="modal fade" id="hireModal" tabindex="-1" aria-labelledby="hireModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="hireModalLabel">招聘员工</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="hireForm">
                    <div class="mb-3">
                        <label for="departmentSelect" class="form-label">选择部门</label>
                        <select class="form-select" id="departmentSelect" required>
                            <option value="">请选择部门</option>
                            {% for department in departments %}
                            <option value="{{ department.name }}">{{ department.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                </form>
                
                <div class="mt-4">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h6>候选人列表</h6>
                        <div>
                            <button class="btn btn-sm btn-secondary me-2" onclick="refreshCandidates()">
                                <i class="bi bi-arrow-repeat"></i> 刷新
                            </button>
                            <button class="btn btn-sm btn-primary" onclick="batchHireCandidates()">
                                <i class="bi bi-people"></i> 批量招聘
                            </button>
                        </div>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-bordered" id="candidatesTable">
                            <thead class="thead-dark">
                                <tr>
                                    <th><input type="checkbox" id="selectAllCheckbox" onchange="toggleAllCheckboxes()"></th>
                                    <th>姓名</th>
                                    <th>等级</th>
                                    <th>基础工资</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="candidatesList">
                                <!-- 候选人将通过JavaScript动态加载 -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // 页面加载时获取候选人列表
    document.addEventListener('DOMContentLoaded', function() {
        const departmentSelect = document.getElementById('departmentSelect');
        departmentSelect.addEventListener('change', loadCandidates);
        
        // 添加每页显示条数选择事件监听
        const perPageSelect = document.getElementById('perPageSelect');
        if (perPageSelect) {
            perPageSelect.addEventListener('change', function() {
                updatePageParams({per_page: this.value, page: 1});
            });
        }
        
        // 初始化筛选条件
        {% if filter_department %}
        document.getElementById('filterDepartment').value = '{{ filter_department }}';
        {% endif %}
        {% if filter_level %}
        document.getElementById('filterLevel').value = '{{ filter_level }}';
        {% endif %}
    });
    
    // 更新页面参数
    function updatePageParams(params) {
        const url = new URL(window.location);
        for (const [key, value] of Object.entries(params)) {
            if (value === '' || value === null || value === undefined) {
                url.searchParams.delete(key);
            } else {
                url.searchParams.set(key, value);
            }
        }
        window.location.href = url.toString();
    }
    
    // 应用筛选条件
    function applyFilters() {
        const department = document.getElementById('filterDepartment').value;
        const level = document.getElementById('filterLevel').value;
        updatePageParams({department: department, level: level, page: 1});
    }
    
    // 重置筛选条件
    function resetFilters() {
        document.getElementById('filterDepartment').value = '';
        document.getElementById('filterLevel').value = '';
        updatePageParams({department: null, level: null, page: 1});
    }
    
    // 全选/取消全选员工复选框
    function toggleAllEmployeeCheckboxes() {
        const selectAllCheckbox = document.getElementById('selectAllEmployees');
        const checkboxes = document.querySelectorAll('.employee-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.checked = selectAllCheckbox.checked;
        });
    }
    
    // 批量解雇员工
    function bulkFireEmployees() {
        const selectedCheckboxes = document.querySelectorAll('.employee-checkbox:checked');
        if (selectedCheckboxes.length === 0) {
            alert('请至少选择一个员工');
            return;
        }
        
        const employeeIds = Array.from(selectedCheckboxes).map(cb => parseInt(cb.value));
        const confirmResult = confirm(`确定要解雇这${employeeIds.length}名员工吗？`);
        if (!confirmResult) {
            return;
        }
        
        fetch('/employees/bulk_fire_employees', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({employee_ids: employeeIds})
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(data.message);
                location.reload();
            } else {
                alert('批量解雇失败: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('批量解雇失败');
        });
    }
    
    // 加载候选人列表
    function loadCandidates() {
        const department = document.getElementById('departmentSelect').value;
        if (!department) {
            document.getElementById('candidatesList').innerHTML = '';
            return;
        }
        
        fetch('/employees/get_candidates/' + department)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const candidatesList = document.getElementById('candidatesList');
                let html = '';
                
                data.candidates.forEach((candidate, index) => {
                    html += `
                    <tr>
                        <td><input type="checkbox" class="candidate-checkbox" data-index="${index}" data-candidate='${JSON.stringify(candidate)}'></td>
                        <td>${candidate.name}</td>
                        <td>${candidate.level}</td>
                        <td>¥${candidate.base_salary.toLocaleString()}</td>
                        <td>
                            <button class="btn btn-sm btn-success" onclick="hireCandidate(${index})">
                                <i class="bi bi-person-plus"></i> 招聘
                            </button>
                        </td>
                    </tr>
                    `;
                });
                
                candidatesList.innerHTML = html;
            } else {
                alert('加载候选人失败: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('加载候选人失败');
        });
    }
    
    // 刷新候选人列表
    function refreshCandidates() {
        loadCandidates();
    }
    
    // 招聘指定候选人
    function hireCandidate(candidateIndex) {
        const department = document.getElementById('departmentSelect').value;
        if (!department) {
            alert('请选择部门');
            return;
        }
        
        // 获取候选人数据
        const candidateCheckbox = document.querySelector(`.candidate-checkbox[data-index="${candidateIndex}"]`);
        if (!candidateCheckbox) {
            alert('未找到候选人信息');
            return;
        }
        
        const candidateData = JSON.parse(candidateCheckbox.dataset.candidate);
        
        fetch('/employees/hire_candidate/' + candidateIndex, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({department: department})
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('招聘成功: ' + candidateData.name);
                loadCandidates(); // 重新加载候选人列表
                // 刷新员工列表
                location.reload();
            } else {
                alert('招聘失败: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('招聘失败');
        });
    }
    
    // 批量招聘候选人
    function batchHireCandidates() {
        const department = document.getElementById('departmentSelect').value;
        if (!department) {
            alert('请选择部门');
            return;
        }
        
        // 获取选中的候选人索引
        const selectedCheckboxes = document.querySelectorAll('.candidate-checkbox:checked');
        if (selectedCheckboxes.length === 0) {
            alert('请至少选择一个候选人');
            return;
        }
        
        const candidateData = [];
        const candidateIndices = Array.from(selectedCheckboxes).map(cb => {
            const index = parseInt(cb.dataset.index);
            const data = JSON.parse(cb.dataset.candidate);
            candidateData.push(data);
            return index;
        });
        
        fetch('/employees/bulk_hire_candidates', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                department: department,
                candidate_indices: candidateIndices
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(`成功招聘${data.hired_count}名员工`);
                loadCandidates(); // 重新加载候选人列表
                // 刷新员工列表
                location.reload();
            } else {
                alert('批量招聘失败: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('批量招聘失败');
        });
    }

    // 全选/取消全选功能
    function toggleAllCheckboxes() {
        const selectAllCheckbox = document.getElementById('selectAllCheckbox');
        const checkboxes = document.querySelectorAll('.candidate-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.checked = selectAllCheckbox.checked;
        });
    }
    
    // 解雇员工
    function fireEmployee(employeeId, employeeName) {
        if (confirm(`确定要解雇员工 ${employeeName} 吗？`)) {
            fetch('/employees/fire', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({employee_id: employeeId})
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert(data.message);
                    location.reload();
                } else {
                    alert('解雇失败: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('解雇失败');
            });
        }
    }
</script>
{% endblock %}
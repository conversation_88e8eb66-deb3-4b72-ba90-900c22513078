{% extends "base.html" %}

{% block title %}员工管理 - {{ hotel.name }}{% endblock %}

{% block content %}
<div class="row mb-3">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <h2><i class="bi bi-people-fill text-success me-2"></i>员工管理</h2>
            <a href="{{ url_for('main.index') }}" class="btn btn-outline-secondary">
                <i class="bi bi-house-fill me-1"></i>返回首页
            </a>
        </div>
    </div>
</div>

<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">员工概况</h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-md-3">
                        <h4 class="text-primary">{{ total_employees }}</h4>
                        <small class="text-muted">员工总数</small>
                    </div>
                    <div class="col-md-3">
                        <h4 class="text-success">¥{{ "{:,}".format(total_salary) }}</h4>
                        <small class="text-muted">月薪总额</small>
                    </div>
                    <div class="col-md-3">
                        <h4 class="text-info">{{ "%.1f"|format(avg_level) }}</h4>
                        <small class="text-muted">平均等级</small>
                    </div>
                    <div class="col-md-3">
                        <h4 class="text-warning">{{ "%.1f"|format(avg_satisfaction) }}%</h4>
                        <small class="text-muted">平均满意度</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">员工列表</h5>
                <button class="btn btn-primary btn-sm" onclick="showHireModal()">
                    <i class="bi bi-person-plus-fill me-1"></i>招聘员工
                </button>
            </div>
            <div class="card-body">
                {% if employees %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>姓名</th>
                                <th>部门</th>
                                <th>等级</th>
                                <th>月薪</th>
                                <th>满意度</th>
                                <th>工作年限</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for employee in employees %}
                            <tr>
                                <td>{{ employee.name }}</td>
                                <td>
                                    <span class="badge bg-primary">{{ employee.department }}</span>
                                </td>
                                <td>{{ employee.level }}级</td>
                                <td>¥{{ "{:,}".format(employee.salary) }}</td>
                                <td>
                                    <div class="progress" style="width: 80px; height: 20px;">
                                        <div class="progress-bar" style="width: {{ employee.satisfaction }}%">
                                            {{ employee.satisfaction }}%
                                        </div>
                                    </div>
                                </td>
                                <td>{{ employee.years_worked }}年</td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <button class="btn btn-outline-info" onclick="viewEmployee({{ employee.id }})">
                                            <i class="bi bi-eye"></i>
                                        </button>
                                        <button class="btn btn-outline-warning" onclick="promoteEmployee({{ employee.id }})">
                                            <i class="bi bi-arrow-up"></i>
                                        </button>
                                        <button class="btn btn-outline-danger" onclick="fireEmployee({{ employee.id }})">
                                            <i class="bi bi-person-dash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="bi bi-people display-1 text-muted"></i>
                    <h4 class="text-muted mt-3">暂无员工</h4>
                    <p class="text-muted">点击"招聘员工"按钮开始招聘</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function showHireModal() {
    showMessage('招聘功能开发中...', 'info');
}

function viewEmployee(employeeId) {
    showMessage('员工详情功能开发中...', 'info');
}

function promoteEmployee(employeeId) {
    if (confirm('确定要提升这名员工吗？')) {
        showMessage('员工提升功能开发中...', 'info');
    }
}

function fireEmployee(employeeId) {
    if (confirm('确定要解雇这名员工吗？此操作不可撤销！')) {
        showMessage('员工解雇功能开发中...', 'info');
    }
}
</script>
{% endblock %}

{% extends "base.html" %}

{% block title %}员工管理 - {{ hotel.name }}{% endblock %}

{% block content %}
<div class="row mb-3">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <h2><i class="bi bi-people-fill text-success me-2"></i>员工管理</h2>
            <a href="{{ url_for('main.index') }}" class="btn btn-outline-secondary">
                <i class="bi bi-house-fill me-1"></i>返回首页
            </a>
        </div>
    </div>
</div>

<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">员工概况</h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-md-3">
                        <h4 class="text-primary">{{ total_employees }}</h4>
                        <small class="text-muted">员工总数</small>
                    </div>
                    <div class="col-md-3">
                        <h4 class="text-success">¥{{ "{:,}".format(total_salary) }}</h4>
                        <small class="text-muted">月薪总额</small>
                    </div>
                    <div class="col-md-3">
                        <h4 class="text-info">{{ "%.1f"|format(avg_level) }}</h4>
                        <small class="text-muted">平均等级</small>
                    </div>
                    <div class="col-md-3">
                        <h4 class="text-warning">{{ "%.1f"|format(avg_satisfaction) }}%</h4>
                        <small class="text-muted">平均满意度</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">员工列表</h5>
                <button class="btn btn-primary btn-sm" onclick="showHireModal()">
                    <i class="bi bi-person-plus-fill me-1"></i>招聘员工
                </button>
            </div>
            <div class="card-body">
                {% if employees %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>姓名</th>
                                <th>部门</th>
                                <th>等级</th>
                                <th>月薪</th>
                                <th>满意度</th>
                                <th>工作年限</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for employee in employees %}
                            <tr>
                                <td>{{ employee.name }}</td>
                                <td>
                                    <span class="badge bg-primary">{{ employee.department }}</span>
                                </td>
                                <td>{{ employee.level }}级</td>
                                <td>¥{{ "{:,}".format(employee.salary) }}</td>
                                <td>
                                    <div class="progress" style="width: 80px; height: 20px;">
                                        <div class="progress-bar" style="width: {{ employee.satisfaction }}%">
                                            {{ employee.satisfaction }}%
                                        </div>
                                    </div>
                                </td>
                                <td>{{ employee.years_worked }}年</td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <button class="btn btn-outline-info" onclick="viewEmployee({{ employee.id }})">
                                            <i class="bi bi-eye"></i>
                                        </button>
                                        <button class="btn btn-outline-warning" onclick="promoteEmployee({{ employee.id }})">
                                            <i class="bi bi-arrow-up"></i>
                                        </button>
                                        <button class="btn btn-outline-danger" onclick="fireEmployee({{ employee.id }})">
                                            <i class="bi bi-person-dash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="bi bi-people display-1 text-muted"></i>
                    <h4 class="text-muted mt-3">暂无员工</h4>
                    <p class="text-muted">点击"招聘员工"按钮开始招聘</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- 招聘候选人模态框 -->
<div class="modal fade" id="hireModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-person-plus-fill me-2"></i>招聘员工
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row" id="candidatesContainer">
                    <!-- 候选人列表将通过JavaScript动态加载 -->
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function showHireModal() {
    // 获取候选人列表
    apiRequest('/employees/get_candidates_list')
    .then(data => {
        if (data.success) {
            displayCandidates(data.candidates);
            const modal = new bootstrap.Modal(document.getElementById('hireModal'));
            modal.show();
        } else {
            showMessage(data.message, 'error');
        }
    })
    .catch(error => {
        showMessage('获取候选人列表失败', 'error');
    });
}

function displayCandidates(candidates) {
    const container = document.getElementById('candidatesContainer');
    container.innerHTML = '';

    if (candidates.length === 0) {
        container.innerHTML = `
            <div class="col-12 text-center py-4">
                <i class="bi bi-people display-1 text-muted"></i>
                <h4 class="text-muted mt-3">暂无候选人</h4>
                <p class="text-muted">请稍后再试</p>
            </div>
        `;
        return;
    }

    candidates.forEach(candidate => {
        const candidateCard = `
            <div class="col-md-6 mb-3">
                <div class="card border-0 bg-light h-100">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-start mb-2">
                            <h6 class="card-title mb-0">${candidate.name}</h6>
                            <span class="badge bg-primary">${candidate.department}</span>
                        </div>
                        <div class="mb-2">
                            <small class="text-muted">等级：</small>
                            <span class="fw-bold">${candidate.level}级</span>
                        </div>
                        <div class="mb-2">
                            <small class="text-muted">期望薪资：</small>
                            <span class="text-success">¥${candidate.salary.toLocaleString()}/月</span>
                        </div>
                        <div class="mb-2">
                            <small class="text-muted">满意度：</small>
                            <span class="text-info">${candidate.satisfaction}%</span>
                        </div>
                        <div class="mb-3">
                            <small class="text-muted">技能：</small>
                            <div class="mt-1">
                                ${candidate.skills.map(skill => `<span class="badge bg-secondary me-1">${skill}</span>`).join('')}
                            </div>
                        </div>
                        <div class="d-grid">
                            <button class="btn btn-success btn-sm" onclick="hireEmployee('${candidate.id}')">
                                <i class="bi bi-person-check me-1"></i>招聘
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
        container.innerHTML += candidateCard;
    });
}

function hireEmployee(candidateId) {
    if (confirm('确定要招聘这名员工吗？')) {
        apiRequest('/employees/hire', {
            method: 'POST',
            body: JSON.stringify({candidate_id: candidateId})
        })
        .then(data => {
            if (data.success) {
                showMessage(data.message, 'success');
                const modal = bootstrap.Modal.getInstance(document.getElementById('hireModal'));
                modal.hide();
                setTimeout(() => location.reload(), 1000);
            } else {
                showMessage(data.message, 'error');
            }
        })
        .catch(error => {
            showMessage('招聘失败', 'error');
        });
    }
}

function viewEmployee(employeeId) {
    showMessage('员工详情功能开发中...', 'info');
}

function promoteEmployee(employeeId) {
    if (confirm('确定要提升这名员工吗？这将增加其薪资。')) {
        apiRequest('/employees/promote', {
            method: 'POST',
            body: JSON.stringify({employee_id: employeeId})
        })
        .then(data => {
            if (data.success) {
                showMessage(data.message, 'success');
                setTimeout(() => location.reload(), 1000);
            } else {
                showMessage(data.message, 'error');
            }
        })
        .catch(error => {
            showMessage('提升员工失败', 'error');
        });
    }
}

function fireEmployee(employeeId) {
    if (confirm('确定要解雇这名员工吗？此操作不可撤销！')) {
        apiRequest('/employees/fire', {
            method: 'POST',
            body: JSON.stringify({employee_id: employeeId})
        })
        .then(data => {
            if (data.success) {
                showMessage(data.message, 'success');
                setTimeout(() => location.reload(), 1000);
            } else {
                showMessage(data.message, 'error');
            }
        })
        .catch(error => {
            showMessage('解雇员工失败', 'error');
        });
    }
}
</script>
{% endblock %}

from flask import render_template, request, jsonify, session
from app import db
from app.models import Hotel, Employee, Department, FinancialRecord
from app.main.utils import get_current_hotel
import json
import logging
import random

logger = logging.getLogger(__name__)

from app.employees import bp


def generate_candidate_employees(hotel_level):
    """根据酒店等级生成候选人列表"""
    candidates = []
    
    # 姓氏列表
    surnames = ["张", "李", "王", "刘", "陈", "杨", "赵", "黄", "周", "吴", 
                "徐", "孙", "胡", "朱", "高", "林", "何", "郭", "马", "罗",
                "梁", "宋", "郑", "谢", "韩", "唐", "冯", "于", "董", "萧"]
    
    # 常用字列表（用于生成名字）
    given_names = ["伟", "芳", "娜", "秀英", "敏", "静", "丽", "强", "磊", "军",
                   "洋", "勇", "艳", "杰", "娟", "涛", "明", "超", "秀兰", "霞",
                   "平", "刚", "桂英", "辉", "倩", "婷", "帅", "旭", "莹", "凯",
                   "华", "玲", "波", "宁", "雪", "博", "丹", "慧", "楠", "曼",
                   "峰", "荣", "颖", "浩", "文", "斌", "涵", "帆", "国庆", "建军",
                   "欢", "瑜", "蓉", "洁", "雯", "彬", "亮", "琴", "晓", "霞",
                   "晶", "慧", "丹", "田", "红", "梅", "玉", "军", "莉", "文",
                   "利", "琴", "兰", "雪", "丽", "建", "文", "利", "琴", "兰"]

    # 生成10个候选人（固定数量）
    for i in range(10):
        # 基础工资根据等级确定（根据优化需求文档修正）
        level = "初级"
        base_salary = 3000  # 初级员工基础工资3000元/月

        # 根据酒店等级决定是否生成更高等级的员工
        if hotel_level >= 2:
            # 50%概率出现中级员工
            if random.random() < 0.5:
                level = "中级"
                base_salary = 5000  # 中级员工基础工资5000元/月

        if hotel_level >= 3:
            # 30%概率出现高级员工
            if random.random() < 0.3:
                level = "高级"
                base_salary = 8000  # 高级员工基础工资8000元/月

        if hotel_level >= 4:
            # 10%概率出现特级员工
            if random.random() < 0.1:
                level = "特级"
                base_salary = 15000  # 特级员工基础工资15000元/月

        # 生成随机姓名（2-4个中文字符）
        surname = random.choice(surnames)
        # 随机决定名字长度（1-3个字）
        name_length = random.randint(1, 3)
        given_name = ""
        for _ in range(name_length):
            given_name += random.choice(given_names)
        name = surname + given_name

        candidates.append({
            "name": name,
            "level": level,
            "base_salary": base_salary
        })

    return candidates


@bp.route('/management')
def management():
    """员工管理页面"""
    hotel = Hotel.query.first()
    if not hotel:
        return "酒店数据未初始化", 500

    # 获取筛选参数
    filter_department = request.args.get('department', '')
    filter_level = request.args.get('level', '')
    
    # 获取分页参数
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 10, type=int)  # 默认每页显示10条记录
    
    # 构建查询
    query = Employee.query.filter_by(hotel_id=hotel.id)
    
    # 应用筛选条件
    if filter_department:
        query = query.filter_by(department=filter_department)
    
    if filter_level:
        query = query.filter_by(level=filter_level)
    
    # 获取员工数据（分页）
    employees = query.paginate(page=page, per_page=per_page, error_out=False)
    
    # 获取已解锁的部门数据（用于招聘）
    departments = Department.query.filter_by(hotel_id=hotel.id, is_unlocked=True).all()
    
    # 获取所有部门数据（用于筛选）
    all_departments = Department.query.filter_by(hotel_id=hotel.id).all()

    # 生成候选人列表
    candidates = generate_candidate_employees(hotel.level)
    
    # 将候选人列表存储到会话中，以便后续操作使用
    session['candidates'] = candidates
    
    # 计算员工统计数据
    all_employees = Employee.query.filter_by(hotel_id=hotel.id).all()
    total_employees = len(all_employees)
    total_salary = sum(emp.salary for emp in all_employees)
    # 由于Employee模型没有level数字字段，暂时使用固定值
    avg_level = 2.5  # 平均等级（初级=1，中级=2，高级=3，特级=4）
    avg_satisfaction = 75.0  # 固定满意度值，因为模型中没有这个字段

    return render_template('employees.html',
                         hotel=hotel,
                         employees=employees.items,  # 使用.items获取实际的员工列表
                         pagination=employees,  # 传递分页对象
                         departments=departments,  # 用于招聘的已解锁部门
                         all_departments=all_departments,  # 用于筛选的所有部门
                         candidates=candidates,
                         total_employees=total_employees,
                         total_salary=total_salary,
                         avg_level=avg_level,
                         avg_satisfaction=avg_satisfaction,
                         per_page=per_page,  # 传递每页记录数到模板
                         filter_department=filter_department,  # 传递筛选条件到模板
                         filter_level=filter_level)  # 传递筛选条件到模板


@bp.route('/get_candidates_list')
def get_candidates_list():
    """获取招聘候选人列表"""
    try:
        hotel = get_current_hotel()
        if not hotel:
            return jsonify({"success": False, "message": "酒店数据未初始化"}), 500

        # 生成随机候选人
        import random

        departments = ["前台", "客房", "餐饮", "保安", "清洁", "维修"]
        names = ["张三", "李四", "王五", "赵六", "钱七", "孙八", "周九", "吴十", "郑十一", "王十二"]
        skills_pool = ["沟通能力", "服务意识", "团队合作", "问题解决", "时间管理", "专业技能", "语言能力", "计算机技能"]

        candidates = []
        for i in range(6):  # 生成6个候选人
            candidate = {
                "id": f"candidate_{i}",
                "name": random.choice(names),
                "department": random.choice(departments),
                "level": random.randint(1, 5),
                "salary": random.randint(3000, 8000),
                "satisfaction": random.randint(70, 95),
                "skills": random.sample(skills_pool, random.randint(2, 4))
            }
            candidates.append(candidate)

        return jsonify({"success": True, "candidates": candidates})
    except Exception as e:
        logger.error(f"获取候选人列表时出错: {e}")
        return jsonify({"success": False, "message": "获取候选人列表失败"}), 500


@bp.route('/hire', methods=['POST'])
def hire_employee():
    """招聘员工"""
    try:
        hotel = get_current_hotel()
        if not hotel:
            return jsonify({"success": False, "message": "酒店数据未初始化"}), 500

        data = request.get_json()
        candidate_id = data.get('candidate_id')

        if not candidate_id:
            return jsonify({"success": False, "message": "候选人ID不能为空"}), 400

        # 这里应该根据candidate_id获取候选人信息
        # 为了简化，我们生成一个随机员工
        import random

        departments = ["前台", "客房", "餐饮", "保安", "清洁", "维修"]
        names = ["张三", "李四", "王五", "赵六", "钱七", "孙八"]

        # 创建新员工
        levels = ["初级", "中级", "高级"]
        base_salary = random.randint(3000, 6000)

        new_employee = Employee(
            hotel_id=hotel.id,
            name=random.choice(names),
            department=random.choice(departments),
            level=random.choice(levels),
            base_salary=base_salary,
            salary=base_salary,
            years_worked=0
        )

        # 检查资金是否足够支付首月工资
        if hotel.money < new_employee.salary:
            return jsonify({"success": False, "message": "资金不足，无法支付员工工资"}), 400

        # 扣除首月工资
        hotel.money -= new_employee.salary

        # 记录财务记录
        financial_record = FinancialRecord(
            hotel_id=hotel.id,
            record_date=hotel.date,
            expense=new_employee.salary,
            description=f"招聘员工{new_employee.name}，首月工资"
        )

        db.session.add(new_employee)
        db.session.add(financial_record)

        # 使用安全的数据库操作
        from app.main.utils import safe_database_operation

        def commit_operation():
            db.session.commit()
            return True

        result = safe_database_operation(commit_operation)
        if result:
            return jsonify({
                "success": True,
                "message": f"成功招聘{new_employee.name}到{new_employee.department}部门！"
            })
        else:
            return jsonify({"success": False, "message": "招聘员工失败，请重试"}), 500

    except Exception as e:
        db.session.rollback()
        logger.error(f"招聘员工时出错: {e}")
        return jsonify({"success": False, "message": "招聘员工失败"}), 500


@bp.route('/promote', methods=['POST'])
def promote_employee():
    """提升员工"""
    try:
        hotel = get_current_hotel()
        if not hotel:
            return jsonify({"success": False, "message": "酒店数据未初始化"}), 500

        data = request.get_json()
        employee_id = data.get('employee_id')

        employee = Employee.query.filter_by(id=employee_id, hotel_id=hotel.id).first()
        if not employee:
            return jsonify({"success": False, "message": "员工不存在"}), 400

        if employee.level >= 10:
            return jsonify({"success": False, "message": "员工已达到最高等级"}), 400

        # 计算提升费用
        promotion_cost = employee.salary * 0.5  # 提升费用为当前工资的50%

        if hotel.money < promotion_cost:
            return jsonify({"success": False, "message": "资金不足"}), 400

        # 提升员工
        old_level = employee.level
        old_salary = employee.salary

        # 提升等级（字符串类型）
        level_map = {"初级": "中级", "中级": "高级", "高级": "特级"}
        if employee.level in level_map:
            employee.level = level_map[employee.level]

        employee.salary = int(employee.salary * 1.2)  # 工资增加20%
        # 注意：Employee模型没有satisfaction字段，所以不更新满意度

        # 扣除提升费用
        hotel.money -= promotion_cost

        # 记录财务记录
        financial_record = FinancialRecord(
            hotel_id=hotel.id,
            record_date=hotel.date,
            expense=promotion_cost,
            description=f"提升员工{employee.name}从{old_level}级到{employee.level}级"
        )

        db.session.add(financial_record)

        # 使用安全的数据库操作
        from app.main.utils import safe_database_operation

        def commit_operation():
            db.session.commit()
            return True

        result = safe_database_operation(commit_operation)
        if result:
            return jsonify({
                "success": True,
                "message": f"{employee.name}成功提升到{employee.level}级！工资从¥{old_salary:,}增加到¥{employee.salary:,}"
            })
        else:
            return jsonify({"success": False, "message": "提升员工失败，请重试"}), 500

    except Exception as e:
        db.session.rollback()
        logger.error(f"提升员工时出错: {e}")
        return jsonify({"success": False, "message": "提升员工失败"}), 500


@bp.route('/fire', methods=['POST'])
def fire_employee():
    """解雇员工"""
    try:
        hotel = get_current_hotel()
        if not hotel:
            return jsonify({"success": False, "message": "酒店数据未初始化"}), 500

        data = request.get_json()
        employee_id = data.get('employee_id')

        employee = Employee.query.filter_by(id=employee_id, hotel_id=hotel.id).first()
        if not employee:
            return jsonify({"success": False, "message": "员工不存在"}), 400

        # 计算遣散费
        severance_pay = employee.salary * 0.5  # 遣散费为当前工资的50%

        if hotel.money < severance_pay:
            return jsonify({"success": False, "message": "资金不足支付遣散费"}), 400

        employee_name = employee.name
        employee_department = employee.department

        # 扣除遣散费
        hotel.money -= severance_pay

        # 记录财务记录
        financial_record = FinancialRecord(
            hotel_id=hotel.id,
            record_date=hotel.date,
            expense=severance_pay,
            description=f"解雇员工{employee_name}，支付遣散费"
        )

        # 删除员工记录
        db.session.delete(employee)
        db.session.add(financial_record)

        # 使用安全的数据库操作
        from app.main.utils import safe_database_operation

        def commit_operation():
            db.session.commit()
            return True

        result = safe_database_operation(commit_operation)
        if result:
            return jsonify({
                "success": True,
                "message": f"已解雇{employee_department}部门的{employee_name}，支付遣散费¥{severance_pay:,}"
            })
        else:
            return jsonify({"success": False, "message": "解雇员工失败，请重试"}), 500

    except Exception as e:
        db.session.rollback()
        logger.error(f"解雇员工时出错: {e}")
        return jsonify({"success": False, "message": "解雇员工失败"}), 500


@bp.route('/get_candidates/<department>')
def get_candidates(department):
    """获取指定部门的候选人列表"""
    try:
        hotel = Hotel.query.first()
        if not hotel:
            return jsonify({"success": False, "message": "酒店数据不存在"}), 400

        # 检查部门是否已解锁
        dept = Department.query.filter_by(hotel_id=hotel.id, name=department).first()
        if not dept or not dept.is_unlocked:
            return jsonify({"success": False, "message": "该部门尚未解锁"}), 400

        # 生成候选人列表
        candidates = generate_candidate_employees(hotel.level)
        
        # 将候选人列表存储到会话中，以便后续操作使用
        session['candidates'] = candidates
        
        return jsonify({
            "success": True,
            "candidates": candidates
        })
    except Exception as e:
        logger.error(f"获取候选人列表失败: {e}")
        return jsonify({"success": False, "message": "获取候选人列表失败"}), 500


@bp.route('/hire_candidate/<int:candidate_index>', methods=['POST'])
def hire_candidate(candidate_index):
    """招聘指定索引的候选人"""
    try:
        hotel = Hotel.query.first()
        if not hotel:
            return jsonify({"success": False, "message": "酒店数据不存在"}), 400

        data = request.get_json()
        department = data.get('department')

        if not department:
            return jsonify({"success": False, "message": "请选择部门"}), 400

        # 检查部门是否已解锁
        dept = Department.query.filter_by(hotel_id=hotel.id, name=department).first()
        if not dept or not dept.is_unlocked:
            return jsonify({"success": False, "message": "该部门尚未解锁"}), 400

        # 从会话中获取候选人列表，如果没有则重新生成
        candidates = session.get('candidates')
        if not candidates:
            candidates = generate_candidate_employees(hotel.level)

        if candidate_index < 0 or candidate_index >= len(candidates):
            return jsonify({"success": False, "message": "候选人索引无效"}), 400

        candidate = candidates[candidate_index]

        # 计算工资（基础工资 + 部门加成）
        base_salary = candidate['base_salary']
        department_bonus = dept.bonus_rate if hasattr(dept, 'bonus_rate') else 1.0
        salary = int(base_salary * department_bonus)

        # 创建新员工
        new_employee = Employee(
            hotel_id=hotel.id,
            name=candidate['name'],
            department=department,
            level=candidate['level'],
            base_salary=base_salary,
            salary=salary,
            years_worked=0  # 新员工工龄为0
        )

        db.session.add(new_employee)
        
        # 扣除招聘费用（根据优化需求文档修正）
        recruitment_costs = {
            "初级": 10000,   # 初级员工招聘费用1万元
            "中级": 30000,   # 中级员工招聘费用3万元
            "高级": 80000,   # 高级员工招聘费用8万元
            "特级": 200000   # 特级员工招聘费用20万元
        }
        recruitment_cost = recruitment_costs.get(candidate['level'], 10000)

        # 检查资金是否足够支付招聘费用
        if hotel.money < recruitment_cost:
            return jsonify({"success": False, "message": f"资金不足，招聘{candidate['level']}员工需要{recruitment_cost:,}元"}), 400

        # 扣除招聘费用
        hotel.money -= recruitment_cost

        financial_record = FinancialRecord(
            hotel_id=hotel.id,
            record_date=hotel.date,  # 使用游戏时间而不是默认的当前时间
            expense=recruitment_cost,
            description=f"招聘 {candidate['name']} 的费用"
        )
        db.session.add(financial_record)
        
        db.session.commit()

        return jsonify({"success": True, "message": f"成功招聘{candidate['name']}"})
    except Exception as e:
        db.session.rollback()
        logger.error(f"招聘候选人时出错: {e}")
        return jsonify({"success": False, "message": "招聘失败"}), 500


def validate_employee_data(name, level, base_salary, department):
    """验证员工数据"""
    logger.info(f"开始验证员工数据: name={name}, level={level}, base_salary={base_salary}, department={department}")
    
    # 检查姓名是否为字符串
    if not isinstance(name, str):
        logger.warning(f"姓名不是字符串类型: {name} (类型: {type(name)})")
        return False
    
    # 检查姓名是否为空
    if not name.strip():
        logger.warning("姓名为空")
        return False
    
    # 检查等级是否有效
    valid_levels = ['初级', '中级', '高级', '特级']
    if level not in valid_levels:
        logger.warning(f"无效的员工等级: {level}")
        return False
    
    # 检查基础工资是否为数字且大于0
    try:
        base_salary = int(base_salary)
        if base_salary <= 0:
            logger.warning(f"基础工资必须大于0: {base_salary}")
            return False
    except (ValueError, TypeError):
        logger.warning(f"基础工资不是有效数字: {base_salary}")
        return False
    
    # 检查部门是否为字符串
    if not isinstance(department, str):
        logger.warning(f"部门不是字符串类型: {department} (类型: {type(department)})")
        return False
    
    # 检查部门是否为空
    if not department.strip():
        logger.warning("部门为空")
        return False
    
    logger.info("员工数据验证通过")
    return True


@bp.route('/bulk_hire_candidates', methods=['POST'])
def bulk_hire_candidates():
    """批量招聘候选人"""
    try:
        hotel = Hotel.query.first()
        if not hotel:
            return jsonify({"success": False, "message": "酒店数据不存在"}), 400

        data = request.get_json()
        candidate_indices = data.get('candidate_indices', [])
        department = data.get('department')

        if not department:
            return jsonify({"success": False, "message": "请选择部门"}), 400

        # 检查部门是否已解锁
        dept = Department.query.filter_by(hotel_id=hotel.id, name=department).first()
        if not dept or not dept.is_unlocked:
            return jsonify({"success": False, "message": "该部门尚未解锁"}), 400

        # 从会话中获取候选人列表，如果没有则重新生成
        candidates = session.get('candidates')
        if not candidates:
            candidates = generate_candidate_employees(hotel.level)

        hired_candidates = []
        total_cost = 0

        # 招聘费用标准（根据优化需求文档修正）
        recruitment_costs = {
            "初级": 10000,   # 初级员工招聘费用1万元
            "中级": 30000,   # 中级员工招聘费用3万元
            "高级": 80000,   # 高级员工招聘费用8万元
            "特级": 200000   # 特级员工招聘费用20万元
        }

        # 验证所有索引
        for index in candidate_indices:
            if index < 0 or index >= len(candidates):
                return jsonify({"success": False, "message": f"候选人索引 {index} 无效"}), 400

        # 计算总招聘费用
        for index in candidate_indices:
            candidate = candidates[index]
            recruitment_cost = recruitment_costs.get(candidate['level'], 10000)
            total_cost += recruitment_cost

        # 检查资金是否足够
        if hotel.money < total_cost:
            return jsonify({"success": False, "message": f"资金不足，批量招聘需要{total_cost:,}元"}), 400

        # 批量招聘
        for index in candidate_indices:
            candidate = candidates[index]

            # 计算工资（基础工资 + 部门加成）
            base_salary = candidate['base_salary']
            department_bonus = dept.bonus_rate if hasattr(dept, 'bonus_rate') else 1.0
            salary = int(base_salary * department_bonus)

            # 创建新员工
            new_employee = Employee(
                hotel_id=hotel.id,
                name=candidate['name'],
                department=department,
                level=candidate['level'],
                base_salary=base_salary,
                salary=salary,
                years_worked=0  # 新员工工龄为0
            )

            db.session.add(new_employee)
            hired_candidates.append(candidate['name'])

        # 扣除招聘费用
        if total_cost > 0:
            hotel.money -= total_cost
            financial_record = FinancialRecord(
                hotel_id=hotel.id,
                record_date=hotel.date,  # 使用游戏时间而不是默认的当前时间
                expense=total_cost,
                description=f"批量招聘 {len(hired_candidates)} 名员工的费用"
            )
            db.session.add(financial_record)
        
        db.session.commit()
        
        return jsonify({
            "success": True, 
            "message": f"成功批量招聘 {len(hired_candidates)} 名员工: {', '.join(hired_candidates)}",
            "hired_count": len(hired_candidates)
        })
    except Exception as e:
        db.session.rollback()
        logger.error(f"批量招聘候选人时出错: {e}")
        return jsonify({"success": False, "message": "批量招聘失败"}), 500


@bp.route('/refresh_candidates', methods=['POST'])
def refresh_candidates():
    """刷新候选人列表"""
    try:
        hotel = Hotel.query.first()
        if not hotel:
            return jsonify({"success": False, "message": "酒店数据不存在"}), 400

        # 生成新的候选人列表
        candidates = generate_candidate_employees(hotel.level)
        
        # 更新会话中的候选人列表
        session['candidates'] = candidates
        
        return jsonify({
            "success": True,
            "message": "候选人列表刷新成功",
            "candidates": candidates
        })
    except Exception as e:
        logger.error(f"刷新候选人列表时出错: {e}")
        return jsonify({"success": False, "message": "刷新候选人列表失败"}), 500


@bp.route('/bulk_fire', methods=['POST'])
def bulk_fire_employees():
    """批量解雇员工"""
    try:
        hotel = Hotel.query.first()
        if not hotel:
            return jsonify({"success": False, "message": "酒店数据不存在"}), 400

        data = request.get_json()
        employee_ids = data.get('employee_ids', [])

        if not employee_ids:
            return jsonify({"success": False, "message": "请选择要解雇的员工"}), 400

        # 检查是否是最后一个员工
        employee_count = Employee.query.filter_by(hotel_id=hotel.id).count()
        if employee_count <= len(employee_ids):
            return jsonify({"success": False, "message": "不能解雇所有员工"}), 400

        fired_employees = []
        total_compensation = 0

        # 计算总补偿金
        for employee_id in employee_ids:
            employee = Employee.query.filter_by(id=employee_id, hotel_id=hotel.id).first()
            if employee:
                # 计算补偿金
                base_salary = {
                    "初级": 3000,   # 初级员工基础工资3000元/月
                    "中级": 5000,   # 中级员工基础工资5000元/月
                    "高级": 8000,   # 高级员工基础工资8000元/月
                    "特级": 15000   # 特级员工基础工资15000元/月
                }.get(employee.level, 3000)

                years_worked = getattr(employee, 'years_worked', 0)
                seniority_multiplier = 1 + (years_worked * 0.1)
                compensation = base_salary * seniority_multiplier
                total_compensation += compensation

        # 检查资金是否足够支付总补偿金
        if hotel.money < total_compensation:
            return jsonify({"success": False, "message": f"资金不足，批量解雇补偿需要{total_compensation:,.0f}元"}), 400

        # 执行批量解雇
        for employee_id in employee_ids:
            employee = Employee.query.filter_by(id=employee_id, hotel_id=hotel.id).first()
            if employee:
                fired_employees.append(employee.name)
                db.session.delete(employee)

        if not fired_employees:
            return jsonify({"success": False, "message": "未找到要解雇的员工"}), 404

        # 扣除总补偿金
        hotel.money -= int(total_compensation)

        # 记录批量解雇补偿支出
        compensation_record = FinancialRecord(
            hotel_id=hotel.id,
            record_date=hotel.date,
            description=f"批量解雇 {len(fired_employees)} 名员工的补偿金",
            income=0,
            expense=int(total_compensation)
        )
        db.session.add(compensation_record)

        db.session.commit()

        return jsonify({
            "success": True,
            "message": f"成功批量解雇 {len(fired_employees)} 名员工，支付补偿金{total_compensation:,.0f}元",
            "fired_count": len(fired_employees)
        })
    except Exception as e:
        db.session.rollback()
        logger.error(f"批量解雇员工时出错: {e}")
        return jsonify({"success": False, "message": "批量解雇员工失败"}), 500

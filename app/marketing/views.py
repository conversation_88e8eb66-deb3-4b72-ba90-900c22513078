from flask import render_template, request, jsonify, session
from app import db
from app.models import Hotel, Department, FinancialRecord, GameSetting
import json
import logging

logger = logging.getLogger(__name__)

from app.marketing import bp


def get_marketing_rules():
    """获取营销活动规则"""
    return {
        1: {
            "level": 1,
            "display_name": "网络广告",
            "effect": "提升入住率5%",
            "duration": "7天",
            "cost": 50000,
            "frequency": 3,
            "min_hotel_level": 1,
            "used": 0  # 这个值应该从数据库获取，暂时设为0
        },
        2: {
            "level": 2,
            "display_name": "电视广告",
            "effect": "提升入住率10%",
            "duration": "15天",
            "cost": 200000,
            "frequency": 2,
            "min_hotel_level": 2,
            "used": 0  # 这个值应该从数据库获取，暂时设为0
        },
        3: {
            "level": 3,
            "display_name": "线下推广",
            "effect": "提升入住率15%",
            "duration": "30天",
            "cost": 500000,
            "frequency": 1,
            "min_hotel_level": 3,
            "used": 0  # 这个值应该从数据库获取，暂时设为0
        },
        4: {
            "level": 4,
            "display_name": "商务活动",
            "effect": "提升入住率20%",
            "duration": "30天",
            "cost": 1000000,
            "frequency": 1,
            "min_hotel_level": 4,
            "used": 0  # 这个值应该从数据库获取，暂时设为0
        }
    }


def check_advertise_conditions(hotel):
    """
    检查投放广告的条件
    返回 (can_advertise, cost_or_error_message)
    """
    # 检查营销部是否已解锁
    marketing_dept = Department.query.filter_by(hotel_id=hotel.id, name="营销部", is_unlocked=True).first()
    if not marketing_dept:
        return False, "营销部未解锁"

    # 获取广告费用设置
    cost_setting = GameSetting.query.filter_by(key='marketing_advertise_cost').first()
    if not cost_setting:
        # 默认费用为5000
        cost = 5000
    else:
        try:
            cost = int(cost_setting.value)
        except ValueError:
            cost = 5000

    # 检查资金是否足够
    if hotel.money < cost:
        return False, "资金不足"

    return True, cost


def calculate_advertising_effect(hotel):
    """
    计算广告效果
    返回 (effect_description, effect_multiplier)
    """
    # 基础效果
    base_effect = 0.05  # 5%
    
    # 根据酒店等级增加效果
    level_bonus = (hotel.level - 1) * 0.02  # 每级增加2%
    
    total_effect = base_effect + level_bonus
    effect_description = f"提升入住率{int(total_effect * 100)}%"
    
    return effect_description, total_effect


@bp.route('/management')
def management():
    """市场营销页面"""
    from app.main.utils import get_current_hotel
    hotel = get_current_hotel()
    if not hotel:
        return "酒店数据未初始化", 500

    # 营销活动配置
    campaigns_config = {
        'online_ads': {'name': '网络广告', 'description': '在各大网站投放广告，提升酒店知名度', 'cost': 50000, 'effect': 5, 'duration': 30},
        'tv_ads': {'name': '电视广告', 'description': '在电视台投放广告，覆盖更广泛的受众', 'cost': 200000, 'effect': 10, 'duration': 30},
        'social_media': {'name': '社交媒体推广', 'description': '通过社交媒体平台进行品牌推广', 'cost': 80000, 'effect': 8, 'duration': 30},
        'print_ads': {'name': '报纸杂志广告', 'description': '在报纸和杂志上刊登广告', 'cost': 30000, 'effect': 3, 'duration': 30},
        'outdoor_ads': {'name': '户外广告', 'description': '在户外投放大型广告牌', 'cost': 150000, 'effect': 12, 'duration': 60},
        'event_marketing': {'name': '活动营销', 'description': '举办各种活动来吸引客户', 'cost': 100000, 'effect': 15, 'duration': 7}
    }

    # 获取活跃的营销活动
    from app.models import MarketingCampaign
    active_campaigns_db = MarketingCampaign.query.filter_by(hotel_id=hotel.id, is_active=True).all()

    # 构建营销活动列表
    marketing_campaigns = []
    for campaign_id, config in campaigns_config.items():
        # 查找是否有活跃的活动
        active_campaign = next((c for c in active_campaigns_db if c.campaign_id == campaign_id), None)

        campaign_data = {
            'id': campaign_id,
            'name': config['name'],
            'description': config['description'],
            'cost': config['cost'],
            'effect': config['effect'],
            'duration': config['duration'],
            'is_active': active_campaign is not None,
            'progress': active_campaign.progress if active_campaign else 0,
            'remaining_days': active_campaign.remaining_days if active_campaign else 0
        }
        marketing_campaigns.append(campaign_data)

    # 计算营销概况数据
    active_campaigns_count = len(active_campaigns_db)
    monthly_marketing_budget = sum(campaign.cost for campaign in active_campaigns_db)
    marketing_effect = sum(campaign.effect for campaign in active_campaigns_db)
    brand_awareness = min(100, hotel.reputation / 100)  # 基于声望计算品牌知名度

    # 营销历史
    marketing_history = MarketingCampaign.query.filter_by(hotel_id=hotel.id).order_by(MarketingCampaign.started_at.desc()).limit(10).all()

    return render_template('marketing.html',
                         hotel=hotel,
                         marketing_campaigns=marketing_campaigns,
                         active_campaigns=active_campaigns_count,
                         monthly_marketing_budget=monthly_marketing_budget,
                         marketing_effect=marketing_effect,
                         brand_awareness=int(brand_awareness),
                         marketing_history=marketing_history)


@bp.route('/host_wellness_event', methods=['POST'])
def host_wellness_event():
    """举办康养活动"""
    hotel = Hotel.query.first()
    if not hotel:
        return jsonify({'success': False, 'message': '酒店数据未初始化'}), 500

    data = request.get_json()
    event_type = data.get('event_type')
    days = data.get('days', 7)

    # 检查康养部是否已解锁
    wellness_dept = Department.query.filter_by(hotel_id=hotel.id, name="康养部", is_unlocked=True).first()
    if not wellness_dept:
        return jsonify({'success': False, 'message': '康养部未解锁'}), 400

    # 活动类型和费用映射
    event_costs = {
        "基础康养": 10000,
        "中级康养": 30000,
        "高级康养": 80000
    }

    if event_type not in event_costs:
        return jsonify({'success': False, 'message': '无效的活动类型'}), 400

    cost = event_costs[event_type] * days

    # 检查资金是否足够
    if hotel.money < cost:
        return jsonify({'success': False, 'message': '资金不足'}), 400

    # 扣除费用
    hotel.money -= cost

    # 记录财务记录
    financial_record = FinancialRecord(
        hotel_id=hotel.id,
        record_date=hotel.date,
        expense=cost,
        description=f"举办{event_type}康养活动，持续{days}天"
    )
    db.session.add(financial_record)

    try:
        db.session.commit()
        return jsonify({
            'success': True, 
            'message': f'{event_type}康养活动举办成功，持续{days}天',
            'new_balance': hotel.money
        })
    except Exception as e:
        db.session.rollback()
        logger.error(f"举办康养活动时出错: {e}")
        return jsonify({'success': False, 'message': '康养活动举办失败'}), 500


@bp.route('/launch_ad_campaign', methods=['POST'])
def launch_ad_campaign():
    """投放广告活动"""
    hotel = Hotel.query.first()
    if not hotel:
        return jsonify({'success': False, 'message': '酒店数据未初始化'}), 500

    data = request.get_json()
    ad_type = data.get('ad_type')
    days = data.get('days', 7)

    # 检查营销部是否已解锁
    marketing_dept = Department.query.filter_by(hotel_id=hotel.id, name="营销部", is_unlocked=True).first()
    if not marketing_dept:
        return jsonify({'success': False, 'message': '营销部未解锁'}), 400

    # 广告类型和费用映射
    ad_costs = {
        "报纸广告": 5000,
        "电视广告": 20000,
        "网络广告": 10000
    }

    if ad_type not in ad_costs:
        return jsonify({'success': False, 'message': '无效的广告类型'}), 400

    cost = ad_costs[ad_type] * days

    # 检查资金是否足够
    if hotel.money < cost:
        return jsonify({'success': False, 'message': '资金不足'}), 400

    # 扣除费用
    hotel.money -= cost

    # 记录财务记录
    financial_record = FinancialRecord(
        hotel_id=hotel.id,
        record_date=hotel.date,
        expense=cost,
        description=f"投放{ad_type}，持续{days}天"
    )
    db.session.add(financial_record)

    try:
        db.session.commit()
        return jsonify({
            'success': True, 
            'message': f'{ad_type}投放成功，持续{days}天',
            'new_balance': hotel.money
        })
    except Exception as e:
        db.session.rollback()
        logger.error(f"投放广告时出错: {e}")
        return jsonify({'success': False, 'message': '广告投放失败'}), 500


@bp.route('/advertise', methods=['POST'])
def advertise():
    """投放广告"""
    hotel = Hotel.query.first()
    if not hotel:
        return jsonify({'success': False, 'message': '酒店数据未初始化'}), 500

    # 检查投放广告条件
    can_advertise, result = check_advertise_conditions(hotel)
    if not can_advertise:
        return jsonify({'success': False, 'message': result}), 400
    
    cost = result
    effect = calculate_advertising_effect(hotel)

    # 扣除费用
    hotel.money -= cost

    # 记录财务记录
    financial_record = FinancialRecord(
        hotel_id=hotel.id,
        record_date=hotel.date,
        expense=cost,
        description=f"投放广告，{effect[0]}"
    )
    db.session.add(financial_record)

    try:
        db.session.commit()
        return jsonify({
            'success': True,
            'message': f'广告投放成功，{effect[0]}',
            'new_balance': hotel.money
        })
    except Exception as e:
        db.session.rollback()
        logger.error(f"投放广告时出错: {e}")
        return jsonify({'success': False, 'message': '广告投放失败'}), 500


@bp.route('/start_activity', methods=['POST'])
def start_activity():
    """开始营销活动"""
    hotel = Hotel.query.first()
    if not hotel:
        return jsonify({'success': False, 'message': '酒店数据未初始化'}), 500

    data = request.get_json()
    level = data.get('level')
    cost = data.get('cost')
    effect = data.get('effect')
    duration = data.get('duration')

    # 验证参数
    if not all([level, cost, effect, duration]):
        return jsonify({'success': False, 'message': '参数不完整'}), 400

    try:
        # 将level转换为整数进行比较
        level = int(level)
        cost = int(cost)
    except (ValueError, TypeError):
        return jsonify({'success': False, 'message': '参数类型错误'}), 400

    # 检查营销部是否已解锁
    marketing_dept = Department.query.filter_by(hotel_id=hotel.id, name="营销部", is_unlocked=True).first()
    if not marketing_dept:
        return jsonify({'success': False, 'message': '营销部未解锁'}), 400

    # 活动规则
    activity_rules = {
        1: {"min_hotel_level": 1, "cost": 50000, "effect": "提升入住率5%", "duration": "7天", "display_name": "网络广告"},
        2: {"min_hotel_level": 2, "cost": 200000, "effect": "提升入住率10%", "duration": "15天", "display_name": "电视广告"},
        3: {"min_hotel_level": 3, "cost": 500000, "effect": "提升入住率15%", "duration": "30天", "display_name": "线下推广"},
        4: {"min_hotel_level": 4, "cost": 1000000, "effect": "提升入住率20%", "duration": "30天", "display_name": "商务活动"}
    }

    # 检查活动等级是否有效
    if level not in activity_rules:
        return jsonify({'success': False, 'message': '无效的活动等级'}), 400

    rule = activity_rules[level]
    
    # 检查酒店等级是否满足要求
    if hotel.level < rule["min_hotel_level"]:
        return jsonify({'success': False, 'message': f'需要{rule["min_hotel_level"]}级酒店才能举办此活动'}), 400

    # 检查资金是否足够
    if hotel.money < cost:
        return jsonify({'success': False, 'message': '资金不足'}), 400

    # 扣除费用（营销活动不直接产生收入，而是通过提高入住率间接增加客房收入）
    hotel.money -= cost

    # 记录财务记录（只记录支出）
    financial_record_expense = FinancialRecord(
        hotel_id=hotel.id,
        record_date=hotel.date,
        expense=cost,
        description=f"举办{rule['effect']}的{rule['duration']}活动"
    )
    db.session.add(financial_record_expense)

    try:
        db.session.commit()
        return jsonify({
            'success': True,
            'message': f"{rule['display_name']}投放成功，{rule['effect']}，持续{rule['duration']}",
            'new_balance': hotel.money
        })
    except Exception as e:
        db.session.rollback()
        logger.error(f"投放活动时出错: {e}")
        return jsonify({'success': False, 'message': '活动投放失败'}), 500


@bp.route('/start_campaign', methods=['POST'])
def start_campaign():
    """启动营销活动"""
    try:
        from app.main.utils import get_current_hotel
        from app.models import MarketingCampaign
        from datetime import timedelta

        hotel = get_current_hotel()
        if not hotel:
            return jsonify({'success': False, 'message': '酒店数据未初始化'}), 500

        data = request.get_json()
        campaign_id = data.get('campaign_id')

        # 营销活动配置
        campaigns_config = {
            'online_ads': {'name': '网络广告', 'cost': 50000, 'effect': 5, 'duration': 30},
            'tv_ads': {'name': '电视广告', 'cost': 200000, 'effect': 10, 'duration': 30},
            'social_media': {'name': '社交媒体推广', 'cost': 80000, 'effect': 8, 'duration': 30},
            'print_ads': {'name': '报纸杂志广告', 'cost': 30000, 'effect': 3, 'duration': 30},
            'outdoor_ads': {'name': '户外广告', 'cost': 150000, 'effect': 12, 'duration': 60},
            'event_marketing': {'name': '活动营销', 'cost': 100000, 'effect': 15, 'duration': 7}
        }

        if campaign_id not in campaigns_config:
            return jsonify({'success': False, 'message': '无效的营销活动'}), 400

        # 检查是否已有相同活动在进行
        existing_campaign = MarketingCampaign.query.filter_by(
            hotel_id=hotel.id,
            campaign_id=campaign_id,
            is_active=True
        ).first()

        if existing_campaign:
            return jsonify({'success': False, 'message': '该营销活动已在进行中'}), 400

        campaign_config = campaigns_config[campaign_id]

        # 检查资金是否足够
        if hotel.money < campaign_config['cost']:
            return jsonify({'success': False, 'message': f'资金不足，需要¥{campaign_config["cost"]:,}'}), 400

        # 创建营销活动记录
        campaign = MarketingCampaign(
            hotel_id=hotel.id,
            campaign_id=campaign_id,
            name=campaign_config['name'],
            cost=campaign_config['cost'],
            effect=campaign_config['effect'],
            duration=campaign_config['duration'],
            started_at=hotel.date,
            ends_at=hotel.date + timedelta(days=campaign_config['duration']),
            is_active=True
        )
        db.session.add(campaign)

        # 扣除费用
        hotel.money -= campaign_config['cost']

        # 记录财务记录
        financial_record = FinancialRecord(
            hotel_id=hotel.id,
            record_date=hotel.date,
            expense=campaign_config['cost'],
            description=f"启动{campaign_config['name']}营销活动"
        )
        db.session.add(financial_record)

        # 使用安全的数据库操作
        from app.main.utils import safe_database_operation

        def commit_operation():
            db.session.commit()
            return True

        result = safe_database_operation(commit_operation)
        if result:
            return jsonify({
                'success': True,
                'message': f'{campaign_config["name"]}启动成功！将在{campaign_config["duration"]}天内提升{campaign_config["effect"]}%入住率',
                'new_balance': hotel.money
            })
        else:
            return jsonify({'success': False, 'message': '启动营销活动失败，请重试'}), 500

    except Exception as e:
        db.session.rollback()
        logger.error(f"启动营销活动时出错: {e}")
        return jsonify({'success': False, 'message': '启动营销活动失败'}), 500


@bp.route('/stop_campaign', methods=['POST'])
def stop_campaign():
    """停止营销活动"""
    try:
        from app.main.utils import get_current_hotel
        hotel = get_current_hotel()
        if not hotel:
            return jsonify({'success': False, 'message': '酒店数据未初始化'}), 500

        data = request.get_json()
        campaign_id = data.get('campaign_id')

        # 这里应该从数据库中查找并停止对应的营销活动
        # 目前简化处理，直接返回成功

        return jsonify({
            'success': True,
            'message': '营销活动已停止'
        })
    except Exception as e:
        logger.error(f"停止营销活动时出错: {e}")
        return jsonify({'success': False, 'message': '停止营销活动失败'}), 500
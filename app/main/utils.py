import threading
import time

# 全局变量控制时间线程运行状态
TIME_RUNNING = True

from app import db, create_app
from app.models import Hotel, Room, Employee, Department, FinancialRecord, Achievement, SeasonalEffect, RandomEvent
from datetime import datetime, timedelta
from collections import defaultdict
import random
import logging
import time
import threading

logger = logging.getLogger(__name__)

# 房间价格配置（根据优化需求文档修正）
ROOM_PRICES = {
    "单人间": 300,
    "标准间": 500,
    "大床房": 700,
    "家庭房": 1000,
    "商务间": 1500,
    "行政间": 2000,
    "豪华间": 3000,
    "总统套房": 5000,
    "皇家套房": 8000,
    "总统别墅": 15000,
    "皇宫套房": 30000
}


def get_current_season(date):
    """获取当前季节"""
    month = date.month
    if month in [12, 1, 2]:
        return "冬季"
    elif month in [3, 4, 5]:
        return "春季"
    elif month in [6, 7, 8]:
        return "夏季"
    else:
        return "秋季"


# 定义房间类型及其入住率范围（根据优化需求文档修正）
ROOM_OCCUPANCY_RANGES = {
    '单人间': (0.4, 0.8),      # 60% ± 20%
    '标准间': (0.5, 0.9),      # 70% ± 20%
    '大床房': (0.55, 0.95),    # 75% ± 20%
    '家庭房': (0.45, 0.85),    # 65% ± 20%
    '商务间': (0.6, 1.0),      # 80% ± 20%
    '行政间': (0.65, 1.0),     # 85% ± 20%
    '豪华间': (0.7, 1.0),      # 90% ± 20%
    '总统套房': (0.75, 1.0),   # 95% ± 20%
    '皇家套房': (0.72, 1.0),   # 92% ± 20%
    '总统别墅': (0.74, 1.0),   # 94% ± 20%
    '皇宫套房': (0.76, 1.0)    # 96% ± 20%
}

# 员工等级服务能力配置（根据优化需求文档添加）
EMPLOYEE_SERVICE_CAPACITY = {
    "初级": 1.0,    # 初级员工服务能力1.0
    "中级": 1.5,    # 中级员工服务能力1.5
    "高级": 2.0,    # 高级员工服务能力2.0
    "特级": 3.0     # 特级员工服务能力3.0
}


def get_current_hotel():
    """获取当前酒店实例"""
    return Hotel.query.first()


def calculate_daily_finances(hotel):
    """计算每日财务，为每种房间类型创建独立的财务记录"""
    logger.info(f"开始计算 {hotel.date} 的财务")
    
    # 获取酒店的所有房间
    rooms = Room.query.filter_by(hotel_id=hotel.id).all()
    
    total_income = 0
    
    # 为每种房间类型单独计算收入
    for room in rooms:
        # 确保room.count和room.price是数字类型
        room_count = int(room.count) if room.count and str(room.count).isdigit() else 0
        room_price = int(room.price) if room.price and str(room.price).isdigit() else 0

        if room_count > 0 and room_price > 0:
            # 计算该房间类型的理论最大收入
            max_income = room_count * room_price
            
            # 根据房间类型确定基础入住率范围
            min_rate, max_rate = ROOM_OCCUPANCY_RANGES.get(room.type, (0.5, 1.0))
            
            # 生成随机入住率
            base_occupancy = random.uniform(min_rate, max_rate)
            
            # 应用各种影响因素
            # 营销活动效果
            marketing_events = SeasonalEffect.query.filter(
                SeasonalEffect.hotel_id == hotel.id,
                SeasonalEffect.effect_type == '入住率调整',
                SeasonalEffect.start_date <= hotel.date,
                SeasonalEffect.end_date >= hotel.date
            ).all()
            
            for event in marketing_events:
                base_occupancy *= (1 + event.effect_value / 100)
            
            # 客户满意度影响（满意度越高入住率越高）
            satisfaction_factor = hotel.satisfaction / 100  # 满意度影响因子
            base_occupancy *= (0.8 + 0.4 * satisfaction_factor)  # 满意度影响范围 0.8-1.2倍
            
            # 酒店声望影响
            # 确保reputation_level是数字类型
            try:
                reputation_level = int(hotel.reputation_level) if hotel.reputation_level else 0
            except (ValueError, TypeError):
                reputation_level = 0
            reputation_factor = min(1.0, reputation_level / 10)  # 声望等级影响因子
            base_occupancy *= (1 + 0.1 * reputation_factor)  # 声望等级最多提升10%入住率
            
            # 季节性因素
            season = get_current_season(hotel.date)
            seasonal_effects = SeasonalEffect.query.filter(
                SeasonalEffect.hotel_id == hotel.id,
                SeasonalEffect.season == season,
                SeasonalEffect.start_date <= hotel.date,
                SeasonalEffect.end_date >= hotel.date
            ).all()
            
            for effect in seasonal_effects:
                base_occupancy *= (1 + effect.effect_value / 100)
            
            # 随机事件影响
            random_events = RandomEvent.query.filter(
                RandomEvent.hotel_id == hotel.id,
                RandomEvent.start_date <= hotel.date,
                RandomEvent.end_date >= hotel.date
            ).all()
            
            for event in random_events:
                if event.event_type == '入住率调整':
                    base_occupancy *= (1 + event.effect_value / 100)
            
            # 确保入住率在合理范围内
            occupancy_rate = max(0.0, min(1.0, base_occupancy))
            
            # 计算实际收入
            actual_income = max_income * occupancy_rate
            
            # 为每种房间类型创建独立的财务记录
            room_income_record = FinancialRecord(
                hotel_id=hotel.id,
                record_date=hotel.date,
                description=f'{room.type}日常经营收入',
                income=int(actual_income),
                expense=0
            )
            db.session.add(room_income_record)
            
            logger.info(f"{room.type}收入: {actual_income}")
            total_income += actual_income
    
    # 计算入住客户数和员工数（用于餐饮和康养收入计算）
    total_guests = 0.0
    for room in rooms:
        # 确保room.count是数字类型
        room_count = int(room.count) if room.count and str(room.count).isdigit() else 0
        if room_count > 0:
            min_rate, max_rate = ROOM_OCCUPANCY_RANGES.get(room.type, (0.5, 1.0))
            occupancy_rate = random.uniform(min_rate, max_rate)
            total_guests += room_count * occupancy_rate

    total_employees = Employee.query.filter_by(hotel_id=hotel.id).count()

    # 检查部门解锁状态
    departments = Department.query.filter_by(hotel_id=hotel.id, is_unlocked=True).all()
    unlocked_dept_names = [dept.name for dept in departments]

    # 餐饮收入（需要餐饮部解锁）
    if "餐饮部" in unlocked_dept_names:
        catering_income = total_guests * 20 * hotel.level + total_employees * 5 * hotel.level
        catering_record = FinancialRecord(
            hotel_id=hotel.id,
            record_date=hotel.date,
            description='餐饮部日常收入',
            income=int(catering_income),
            expense=0
        )
        db.session.add(catering_record)
        total_income += catering_income
        logger.info(f"餐饮收入: {catering_income}")

    # 康养收入（需要康养部解锁）
    if "康养部" in unlocked_dept_names:
        wellness_income = total_guests * 15 * hotel.level
        wellness_record = FinancialRecord(
            hotel_id=hotel.id,
            record_date=hotel.date,
            description='康养部日常收入',
            income=int(wellness_income),
            expense=0
        )
        db.session.add(wellness_record)
        total_income += wellness_income
        logger.info(f"康养收入: {wellness_income}")

    # 董事会加成（需要董事会解锁）
    if "董事会" in unlocked_dept_names:
        board_bonus = total_income * 0.1  # 总收入增加10%
        board_record = FinancialRecord(
            hotel_id=hotel.id,
            record_date=hotel.date,
            description='董事会管理加成',
            income=int(board_bonus),
            expense=0
        )
        db.session.add(board_record)
        total_income += board_bonus
        logger.info(f"董事会加成: {board_bonus}")

    # 更新酒店资金
    hotel.money += int(total_income)

    logger.info(f"日期更新: {hotel.date}, 资金: {hotel.money}, 收入: {total_income}")

    return int(total_income)


def update_employees_work_age(hotel):
    """更新员工工龄"""
    try:
        # 更新所有员工的工龄
        employees = Employee.query.filter_by(hotel_id=hotel.id).all()
        updated_count = 0
        for employee in employees:
            employee.work_age += 1
            updated_count += 1
            
        db.session.commit()
        logger.info(f"更新了 {updated_count} 名员工的工龄")
    except Exception as e:
        db.session.rollback()
        logger.error(f"更新员工工龄时出错: {e}")


def promote_employees(hotel):
    """年初检查并晋升员工"""
    try:
        # 获取所有员工
        employees = Employee.query.filter_by(hotel_id=hotel.id).all()
        
        # 定义晋升规则（工龄要求）
        promotion_rules = {
            "初级": 2,   # 初级员工工作2年后可晋升为中级
            "中级": 3,   # 中级员工工作3年后可晋升为高级
            "高级": 5,   # 高级员工工作5年后可晋升为特级
        }
        
        promoted_count = 0
        for employee in employees:
            # 检查是否满足晋升条件
            if employee.level in promotion_rules and employee.work_age >= promotion_rules[employee.level]:
                # 晋升员工
                if employee.level == "初级":
                    employee.level = "中级"
                elif employee.level == "中级":
                    employee.level = "高级"
                elif employee.level == "高级":
                    employee.level = "特级"
                
                # 重置工龄
                employee.work_age = 0
                promoted_count += 1
                
        db.session.commit()
        logger.info(f"晋升了 {promoted_count} 名员工")
    except Exception as e:
        db.session.rollback()
        logger.error(f"晋升员工时出错: {e}")


def deduct_monthly_salaries(hotel):
    """月初扣除员工工资"""
    try:
        # 获取所有员工
        employees = Employee.query.filter_by(hotel_id=hotel.id).all()
        
        total_salary = 0
        salary_records = []
        
        # 计算每个员工的工资（根据优化需求文档修正）
        for employee in employees:
            # 基础工资根据员工等级确定
            base_salary = {
                "初级": 3000,   # 初级员工基础工资3000元/月
                "中级": 5000,   # 中级员工基础工资5000元/月
                "高级": 8000,   # 高级员工基础工资8000元/月
                "特级": 15000   # 特级员工基础工资15000元/月
            }.get(employee.level, 3000)

            # 工龄加成（每年工龄+10%）
            years_worked = getattr(employee, 'years_worked', 0)
            seniority_multiplier = 1 + (years_worked * 0.1)

            # 计算总工资
            salary = base_salary * seniority_multiplier
            total_salary += salary
            
            # 记录工资明细
            salary_records.append({
                "employee_name": employee.name,
                "level": employee.level,
                "years_worked": years_worked,
                "salary": salary
            })

        # 检查财务部是否解锁（财务部解锁后所有支出减少5%）
        finance_dept = Department.query.filter_by(
            hotel_id=hotel.id,
            name="财务部",
            is_unlocked=True
        ).first()

        if finance_dept:
            total_salary *= 0.95  # 财务部优化，支出减少5%
            logger.info("财务部优化效果：工资支出减少5%")

        # 创建工资支出记录
        salary_record = FinancialRecord(
            hotel_id=hotel.id,
            record_date=hotel.date,
            description="员工工资支出",
            income=0,
            expense=int(total_salary)
        )
        db.session.add(salary_record)

        # 扣除酒店资金
        hotel.money -= int(total_salary)
        
        db.session.commit()
        logger.info(f"扣除员工工资共计: {total_salary}")
        
        # 记录详细工资信息到日志
        for record in salary_records:
            logger.info(f"员工 {record['employee_name']} ({record['level']}, 工龄{record['work_age']}年) 工资: {record['salary']}")
            
    except Exception as e:
        db.session.rollback()
        logger.error(f"扣除员工工资时出错: {e}")


def deduct_room_maintenance_fees(hotel):
    """月初扣除房间维护费用（根据优化需求文档添加）"""
    try:
        # 获取所有房间
        rooms = Room.query.filter_by(hotel_id=hotel.id).all()
        total_rooms = sum(room.count for room in rooms)

        if total_rooms > 0:
            # 基础维护费用：每间房间每月100元
            base_maintenance_cost = total_rooms * 100

            # 检查工程部是否解锁（工程部解锁后维护费用减少50%）
            engineering_dept = Department.query.filter_by(
                hotel_id=hotel.id,
                name="工程部",
                is_unlocked=True
            ).first()

            if engineering_dept:
                base_maintenance_cost *= 0.5  # 工程部优化，维护费用减少50%
                logger.info("工程部优化效果：房间维护费用减少50%")

            # 检查财务部是否解锁（财务部解锁后所有支出减少5%）
            finance_dept = Department.query.filter_by(
                hotel_id=hotel.id,
                name="财务部",
                is_unlocked=True
            ).first()

            if finance_dept:
                base_maintenance_cost *= 0.95  # 财务部优化，支出减少5%
                logger.info("财务部优化效果：维护费用减少5%")

            # 创建维护费用支出记录
            maintenance_record = FinancialRecord(
                hotel_id=hotel.id,
                record_date=hotel.date,
                description=f"房间维护费用（{total_rooms}间）",
                income=0,
                expense=int(base_maintenance_cost)
            )
            db.session.add(maintenance_record)

            # 扣除酒店资金
            hotel.money -= int(base_maintenance_cost)

            db.session.commit()
            logger.info(f"扣除房间维护费用: {int(base_maintenance_cost)}元")

    except Exception as e:
        db.session.rollback()
        logger.error(f"扣除房间维护费用时出错: {e}")


def calculate_satisfaction_and_reputation(hotel):
    """计算客户满意度和酒店声望值（根据优化需求文档修正）"""
    try:
        # 基础满意度：50分
        satisfaction = 50.0

        # 酒店星级加成：每星+1分
        satisfaction += hotel.level

        # 房间等级加成
        rooms = Room.query.filter_by(hotel_id=hotel.id).all()
        room_bonus = 0.0
        for room in rooms:
            # 确保room.count是整数类型
            room_count = int(room.count) if room.count else 0
            if room.type == "皇宫套房":
                room_bonus += 10.0 * room_count
            elif room.type == "总统别墅":
                room_bonus += 8.0 * room_count
            elif room.type == "皇家套房":
                room_bonus += 6.0 * room_count
            elif room.type == "总统套房":
                room_bonus += 4.0 * room_count
            elif room.type == "豪华间":
                room_bonus += 2.0 * room_count

        # 房间加成按总房间数平均
        total_rooms = sum(int(room.count) if room.count else 0 for room in rooms)
        if total_rooms > 0:
            satisfaction += room_bonus / total_rooms

        # 部门特殊效果
        departments = Department.query.filter_by(hotel_id=hotel.id, is_unlocked=True).all()
        for dept in departments:
            if dept.name == "安保部":
                satisfaction += 5  # 安保部：+5分
            elif dept.name == "康养部":
                satisfaction += 10  # 康养部：+10分

        # 部门繁忙度影响
        busy_levels = calculate_department_busy_level(hotel)
        # 确保所有值都是数字类型
        valid_levels = []
        for level in busy_levels.values():
            try:
                level_float = float(level) if level is not None else 0.0
                if level_float > 0:
                    valid_levels.append(level_float)
            except (ValueError, TypeError):
                continue

        avg_busy_level = sum(valid_levels) / len(valid_levels) if valid_levels else 0.0

        if avg_busy_level < 50:
            satisfaction += 10
        elif avg_busy_level <= 80:
            satisfaction += 0
        elif avg_busy_level <= 100:
            satisfaction -= 5
        else:
            satisfaction -= 15

        # 限制满意度在0-100之间
        satisfaction = max(0, min(100, satisfaction))

        # 更新酒店满意度
        hotel.satisfaction = round(satisfaction, 2)
        
        # 计算声望值变化
        reputation_change = 0
        
        # 根据满意度调整声望（根据优化需求文档修正）
        if satisfaction >= 90:
            reputation_change += 5
        elif satisfaction >= 80:
            reputation_change += 3
        elif satisfaction >= 70:
            reputation_change += 1
        elif satisfaction >= 60:
            reputation_change += 0
        elif satisfaction >= 50:
            reputation_change -= 1
        elif satisfaction >= 40:
            reputation_change -= 3
        else:  # satisfaction <= 39
            reputation_change -= 5
        
        # 根据财务表现调整声望
        # 获取今天的财务记录
        today_records = FinancialRecord.query.filter_by(
            hotel_id=hotel.id, 
            record_date=hotel.date
        ).all()
        
        daily_profit = sum(record.income - record.expense for record in today_records)
        
        if daily_profit > 100000:  # 日利润大于10万
            reputation_change += 3
        elif daily_profit > 50000:  # 日利润大于5万
            reputation_change += 2
        elif daily_profit > 10000:  # 日利润大于1万
            reputation_change += 1
        elif daily_profit < 0:  # 亏损
            reputation_change -= 1
        
        # 更新声望值
        hotel.reputation = max(0, hotel.reputation + reputation_change)
        
        # 根据声望值确定声望等级（根据优化需求文档修正）
        reputation_levels = [
            (499, "默默无闻"),
            (1999, "小有名气"),
            (4999, "知名酒店"),
            (9999, "著名品牌"),
            (19999, "行业标杆"),
            (49999, "行业领袖"),
            (99999, "国际知名"),
            (float('inf'), "传奇酒店")
        ]
        
        for threshold, level_name in reputation_levels:
            if hotel.reputation < threshold:
                hotel.reputation_level_name = level_name
                break
        
        # 计算声望等级（0-10的整数）
        hotel.reputation_level = min(10, hotel.reputation // 1000)
        
        db.session.commit()
        logger.info(f"更新满意度: {hotel.satisfaction}, 声望: {hotel.reputation} ({hotel.reputation_level_name})")
        
    except Exception as e:
        db.session.rollback()
        logger.error(f"计算满意度和声望时出错: {e}")


def calculate_department_busy_level(hotel):
    """计算部门繁忙度"""
    try:
        busy_levels = {}
        
        # 获取所有部门
        departments = Department.query.filter_by(hotel_id=hotel.id).all()
        
        # 估算客户数（基于房间入住情况）
        rooms = Room.query.filter_by(hotel_id=hotel.id).all()
        estimated_guests = 0
        for room in rooms:
            # 估算每种房间类型的客户数
            occupancy_rate = random.uniform(0.5, 1.0)  # 简化的入住率估算
            if room.type in ['单人间', '标准间']:
                guests_per_room = 1
            elif room.type in ['大床房', '商务间']:
                guests_per_room = 2
            elif room.type in ['家庭房', '豪华间']:
                guests_per_room = 4
            else:
                guests_per_room = 6
            
            # 确保room.count是数字类型
            room_count = int(room.count) if room.count and str(room.count).isdigit() else 0
            estimated_guests += room_count * occupancy_rate * guests_per_room
        
        # 计算各部门繁忙度
        for department in departments:
            # 各部门基础服务能力（每人每天能服务的客户数）
            capacity_per_employee = {
                '前台部': 50,
                '客房部': 8,
                '餐饮部': 10,
                '营销部': 25,
                '安保部': 30,
                '财务部': 40,
                '商务部': 20,
                '康养部': 15,
                '工程部': 25,
                '人事部': 30,
                '董事会': 50
            }
            
            # 获取部门员工并计算总服务能力
            dept_employees = Employee.query.filter_by(
                hotel_id=hotel.id,
                department=department.name
            ).all()

            if dept_employees:
                # 计算部门总服务能力（考虑员工等级）
                total_service_capacity = 0
                for employee in dept_employees:
                    # 基础服务能力 × 员工等级系数
                    base_capacity = capacity_per_employee.get(department.name, 20)
                    level_multiplier = EMPLOYEE_SERVICE_CAPACITY.get(employee.level, 1.0)
                    total_service_capacity += base_capacity * level_multiplier

                # 计算繁忙度（客户数/总服务能力）
                busy_level = min(200, (estimated_guests / total_service_capacity) * 100) if total_service_capacity > 0 else 0
                busy_levels[department.name] = busy_level
            else:
                busy_levels[department.name] = 0
        
        return busy_levels
    except Exception as e:
        logger.error(f"计算部门繁忙度时出错: {e}")
        return {}


def check_random_events(hotel):
    """检查并触发随机事件"""
    try:
        # 定义一些随机事件
        events = [
            {
                "type": "正面",
                "description": "旅游旺季到来，入住率大幅提升",
                "effect_type": "入住率调整",
                "effect_value": 30.0,  # 提升30%入住率
                "duration": 7  # 持续7天
            },
            {
                "type": "负面",
                "description": "附近新开业一家竞争酒店，入住率下降",
                "effect_type": "入住率调整",
                "effect_value": -20.0,  # 下降20%入住率
                "duration": 10  # 持续10天
            },
            {
                "type": "正面",
                "description": "酒店服务质量获得媒体报道，声望提升",
                "effect_type": "声望调整",
                "effect_value": 500.0,  # 增加500声望值
                "duration": 1  # 立即生效
            },
            {
                "type": "负面",
                "description": "酒店设备出现故障，需要维修支出",
                "effect_type": "支出",
                "effect_value": 5000.0,  # 支出5000元
                "duration": 1  # 立即生效
            },
            {
                "type": "正面",
                "description": "酒店承办重要会议，收入增加",
                "effect_type": "收入",
                "effect_value": 20000.0,  # 增加20000元收入
                "duration": 1  # 立即生效
            }
        ]
        
        # 10%的概率触发随机事件
        if random.random() < 0.1:
            # 随机选择一个事件
            event_data = random.choice(events)
            
            # 创建随机事件记录
            event = RandomEvent(
                hotel_id=hotel.id,
                event_type=event_data["type"],
                description=event_data["description"],
                effect_value=event_data["effect_value"],
                start_date=hotel.date,
                end_date=hotel.date + timedelta(days=event_data["duration"])
            )
            
            db.session.add(event)
            db.session.commit()
            
            logger.info(f"触发随机事件: {event_data['description']}")
            
            # 如果是立即生效的事件，直接应用效果
            if event_data["duration"] == 1:
                if event_data["effect_type"] == "声望调整":
                    hotel.reputation += event_data["effect_value"]
                elif event_data["effect_type"] == "支出":
                    hotel.money -= event_data["effect_value"]
                    # 记录支出
                    expense_record = FinancialRecord(
                        hotel_id=hotel.id,
                        record_date=hotel.date,
                        description=f"随机事件支出: {event_data['description']}",
                        income=0,
                        expense=int(event_data["effect_value"])
                    )
                    db.session.add(expense_record)
                elif event_data["effect_type"] == "收入":
                    hotel.money += event_data["effect_value"]
                    # 记录收入
                    income_record = FinancialRecord(
                        hotel_id=hotel.id,
                        record_date=hotel.date,
                        description=f"随机事件收入: {event_data['description']}",
                        income=int(event_data["effect_value"]),
                        expense=0
                    )
                    db.session.add(income_record)
                
                db.session.commit()
            
            return event_data
            
    except Exception as e:
        db.session.rollback()
        logger.error(f"检查随机事件时出错: {e}")
    
    return None


def time_advance(hotel):
    """推进一天"""
    try:
        # 更新日期
        hotel.date += timedelta(days=1)
        hotel.days_elapsed += 1
        
        # 计算每日财务
        if not calculate_daily_finances(hotel):
            return False
            
        # 更新员工工龄
        update_employees_work_age(hotel)
        
        # 年初检查（1月1日）
        if hotel.date.month == 1 and hotel.date.day == 1:
            promote_employees(hotel)
        
        # 月初检查（1号）
        if hotel.date.day == 1:
            deduct_monthly_salaries(hotel)
            deduct_room_maintenance_fees(hotel)
        
        # 更新满意度和声望
        calculate_satisfaction_and_reputation(hotel)
        
        # 检查随机事件（新系统）
        from app.main.random_events import trigger_random_event, check_event_expiry
        check_event_expiry(hotel)
        event = trigger_random_event(hotel)
        if event:
            logger.info(f"触发随机事件: {event.name}")

        # 检查成就（新系统）
        from app.achievements.views import check_achievements as check_new_achievements
        newly_achieved = check_new_achievements(hotel)
        if newly_achieved:
            logger.info(f"新解锁成就: {[a.name for a in newly_achieved]}")

        # 检查旧成就系统（保持兼容）
        check_achievements(hotel)
        
        logger.info(f"时间已推进到: {hotel.date}")
        return True
    except Exception as e:
        logger.error(f"时间推进时出错: {e}")
        db.session.rollback()
        return False


def check_achievements(hotel):
    """检查并解锁成就"""
    try:
        # 资产过亿 - 拥有1亿元资产
        billionaire = Achievement.query.filter_by(hotel_id=hotel.id, name='资产过亿').first()
        if billionaire and not billionaire.achieved:
            if hotel.money >= 100000000:  # 1亿
                billionaire.achieved = True
                billionaire.achieved_date = hotel.date
                logger.info(f"达成成就: {billionaire.name}")

        # 十星级大酒店 - 酒店达到10星
        ten_stars = Achievement.query.filter_by(hotel_id=hotel.id, name='十星级大酒店').first()
        if ten_stars and not ten_stars.achieved:
            if hotel.level >= 10:
                ten_stars.achieved = True
                ten_stars.achieved_date = hotel.date
                logger.info(f"达成成就: {ten_stars.name}")

        # 声望卓著 - 声望值达到50000
        prestigious = Achievement.query.filter_by(hotel_id=hotel.id, name='声望卓著').first()
        if prestigious and not prestigious.achieved:
            if hotel.reputation >= 50000:
                prestigious.achieved = True
                prestigious.achieved_date = hotel.date
                logger.info(f"达成成就: {prestigious.name}")

        # 千人规模 - 雇佣员工数达到1000人
        thousand_employees = Achievement.query.filter_by(hotel_id=hotel.id, name='千人规模').first()
        if thousand_employees and not thousand_employees.achieved:
            employee_count = Employee.query.filter_by(hotel_id=hotel.id).count()
            if employee_count >= 1000:
                thousand_employees.achieved = True
                thousand_employees.achieved_date = hotel.date
                logger.info(f"达成成就: {thousand_employees.name}")

        # 百间客房 - 拥有客房数量达到100间
        hundred_rooms = Achievement.query.filter_by(hotel_id=hotel.id, name='百间客房').first()
        if hundred_rooms and not hundred_rooms.achieved:
            room_count = sum(room.count for room in Room.query.filter_by(hotel_id=hotel.id).all())
            if room_count >= 100:
                hundred_rooms.achieved = True
                hundred_rooms.achieved_date = hotel.date
                logger.info(f"达成成就: {hundred_rooms.name}")

        # 五星级大酒店 - 酒店达到5星
        five_stars = Achievement.query.filter_by(hotel_id=hotel.id, name='五星级大酒店').first()
        if five_stars and not five_stars.achieved:
            if hotel.level >= 5:
                five_stars.achieved = True
                five_stars.achieved_date = hotel.date
                logger.info(f"达成成就: {five_stars.name}")

        # 部门齐全 - 拥有全部10个部门
        all_departments = Achievement.query.filter_by(hotel_id=hotel.id, name='部门齐全').first()
        if all_departments and not all_departments.achieved:
            department_count = Department.query.filter_by(hotel_id=hotel.id).count()
            if department_count >= 10:  # 全部10个部门
                all_departments.achieved = True
                all_departments.achieved_date = hotel.date
                logger.info(f"达成成就: {all_departments.name}")

        db.session.commit()
        
    except Exception as e:
        db.session.rollback()
        logger.error(f"检查成就时出错: {str(e)}")


def start_time_thread():
    """启动时间推进线程"""
    global TIME_RUNNING
    TIME_RUNNING = True
    time_thread = threading.Thread(target=time_thread_function, daemon=True)
    time_thread.start()
    return time_thread


def time_thread_function():
    """时间推进线程函数"""
    global TIME_RUNNING
    logger.info("时间推进线程启动，运行状态: %s", TIME_RUNNING)

    # 获取应用实例
    from app import create_app, db
    app = create_app()

    # 在应用上下文中运行
    with app.app_context():
        while TIME_RUNNING:
            try:
                # 获取酒店信息
                from app.models import Hotel
                hotel = Hotel.query.first()

                if hotel and hotel.time_running:
                    logger.info("推进时间中... 当前日期: %s, 速度: %d倍速", hotel.date, hotel.time_speed or 1)

                    # 推进一天
                    result = time_advance(hotel)
                    if result:
                        db.session.commit()
                        logger.info("时间已推进到: %s", hotel.date)
                    else:
                        logger.error("时间推进失败")

                    # 根据time_speed调整等待时间
                    speed = hotel.time_speed or 1
                    sleep_time = 5.0 / speed  # 1倍速5秒，2倍速2.5秒
                    logger.debug("等待 %.1f 秒后继续推进", sleep_time)
                    time.sleep(sleep_time)
                else:
                    # 时间暂停时等待1秒
                    time.sleep(1)

            except Exception as e:
                logger.error(f"时间推进线程出错: {e}")
                import traceback
                logger.error(traceback.format_exc())
                time.sleep(5)  # 出错时等待5秒后继续


def stop_time_thread():
    """停止时间推进线程"""
    global TIME_RUNNING
    TIME_RUNNING = False
    logger.info("时间推进线程已停止")
from flask import render_template, jsonify, request
from app.main import bp
from app.models import Hotel, Employee, Department, Room, FinancialRecord, GameSetting
from app.models import initialize_game_data as create_initial_data  # 添加缺失的导入
from app.main.utils import (calculate_daily_finances, update_employees_work_age, 
                           promote_employees, deduct_monthly_salaries, get_current_hotel,
                           calculate_satisfaction_and_reputation, calculate_department_busy_level,
                           time_advance)
from app.main.utils import ROOM_OCCUPANCY_RANGES  # 导入房间入住率范围定义
from app import db
from datetime import datetime, timedelta
import random
import logging
import json
import os

logger = logging.getLogger(__name__)


@bp.route('/')
@bp.route('/index')
def index():
    """首页"""
    hotel = get_current_hotel()
    if not hotel:
        # 如果没有酒店数据，创建初始数据
        create_initial_data()
        hotel = get_current_hotel()
    
    # 获取部门信息
    departments = Department.query.filter_by(hotel_id=hotel.id).all()
    
    return render_template('index.html', hotel=hotel, departments=departments)


@bp.route('/api/hotel_info')
def hotel_info():
    """获取酒店信息API"""
    try:
        hotel = Hotel.query.first()
        if not hotel:
            return jsonify({"success": False, "message": "酒店数据未初始化"}), 500

        # 获取员工总数
        employee_count = Employee.query.filter_by(hotel_id=hotel.id).count()
        
        # 获取房间总数
        rooms = Room.query.filter_by(hotel_id=hotel.id).all()
        total_rooms = sum(room.count for room in rooms)
        
        # 获取客人总数（估算）
        total_guests = sum(int(room.count * 0.7) for room in rooms)  # 简化估算
        
        # 获取月度财务数据（这里简化实现，实际应该查询当月数据）
        monthly_income = 100000  # 示例值
        monthly_expense = 50000  # 示例值
        monthly_profit = monthly_income - monthly_expense
        
        # 获取房间信息和入住率数据
        occupancy_rates = calculate_occupancy_rates(hotel)
        
        room_info = {}
        for room in rooms:
            room_info[room.type] = {
                'count': room.count,
                'price': room.price
            }

        return jsonify({
            "success": True,
            "hotel_name": hotel.name,
            "level": hotel.level,
            "current_date": hotel.date.strftime('%Y-%m-%d'),
            "money": hotel.money,
            "days_elapsed": hotel.days_elapsed,
            "reputation": hotel.reputation,
            "reputation_level": hotel.reputation_level,
            "time_running": hotel.time_running,
            "satisfaction": hotel.satisfaction,  # 新增：客户满意度
            "time_speed": getattr(hotel, 'time_speed', 1),  # 新增：时间速度
            "employee_count": employee_count,
            "total_rooms": total_rooms,
            "total_guests": total_guests,
            "monthly_income": monthly_income,
            "monthly_expense": monthly_expense,
            "monthly_profit": monthly_profit,
            "occupancy_rates": occupancy_rates,
            "room_info": room_info
        })
    except Exception as e:
        logger.error(f"获取酒店信息时出错: {e}")
        return jsonify({"success": False, "message": "获取酒店信息失败"}), 500


def calculate_occupancy_rates(hotel):
    """
    计算过去10天的入住率数据
    返回格式: {
        '单人间': [
            {'date': 'YYYY-MM-DD', 'rate': 85.5},
            ...
        ],
        ...
    }
    """
    try:
        # 获取当前日期和10天前的日期
        end_date = hotel.date
        start_date = end_date - timedelta(days=9)  # 包括今天，共10天数据
        
        # 获取酒店的所有房间类型
        rooms = Room.query.filter_by(hotel_id=hotel.id).all()
        room_types = [room.type for room in rooms]
        
        # 为每种房间类型生成入住率数据
        occupancy_data = {}
        
        for room_type in room_types:
            occupancy_data[room_type] = []
            
            # 为过去10天生成数据
            for i in range(10):
                date = start_date + timedelta(days=i)
                
                # 根据房间类型确定基础入住率范围
                min_rate, max_rate = ROOM_OCCUPANCY_RANGES.get(room_type, (0.5, 1.0))
                
                # 生成随机入住率（模拟真实数据）
                base_occupancy = random.uniform(min_rate, max_rate)
                
                # 应用各种影响因素
                # 客户满意度影响
                satisfaction_factor = hotel.satisfaction / 100
                base_occupancy *= (0.8 + 0.4 * satisfaction_factor)
                
                # 酒店声望影响
                reputation_factor = min(1.0, hotel.reputation_level / 10)
                base_occupancy *= (1 + 0.1 * reputation_factor)
                
                # 季节性因素
                from app.main.utils import get_current_season
                season = get_current_season(date)
                from app.models import SeasonalEffect
                seasonal_effects = SeasonalEffect.query.filter(
                    SeasonalEffect.hotel_id == hotel.id,
                    SeasonalEffect.season == season,
                    SeasonalEffect.start_date <= date,
                    SeasonalEffect.end_date >= date
                ).all()
                
                for effect in seasonal_effects:
                    base_occupancy *= (1 + effect.effect_value / 100)
                
                # 确保入住率在合理范围内
                occupancy_rate = max(0.0, min(1.0, base_occupancy))
                
                occupancy_data[room_type].append({
                    'date': date.strftime('%Y-%m-%d'),
                    'rate': round(occupancy_rate * 100, 1)
                })
        
        return occupancy_data
    except Exception as e:
        logger.error(f"计算入住率时出错: {e}")
        # 返回默认数据
        return {}
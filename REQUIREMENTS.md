# 酒店管理系统需求文档

## 1. 系统概述
酒店管理系统是一个模拟经营类游戏，玩家扮演酒店经营者，通过管理员工、部门、房间等资源，提升酒店等级和服务质量，最终成为酒店大亨。游戏从1990年开始，玩家需要从一星级酒店逐步发展到九星级豪华酒店集团。

## 2. 功能需求

### 2.1 基础信息管理

#### 2.1.1 酒店基本信息
- 酒店名称（可自定义修改）
- 酒店等级（1-9星，通过满足条件逐级升级）
- 当前日期（从1990年1月1日开始）
- 资金余额（初始100万元）
- 运营天数（累计经营天数）
- 时间运行状态（运行/暂停）
- 时间速度（1倍速/2倍速）
- 客户满意度（0-100分）
- 声望值（累计声望点数）
- 声望等级（基于声望值的等级称号）

#### 2.1.2 时间系统
- 系统时间以天为单位自动推进
  - 1倍速：每5秒推进一天
  - 2倍速：每2.5秒推进一天
- 可以暂停/继续时间运行
- 支持手动推进一天功能
- 时间推进时会触发以下操作：
  - 更新酒店日期和运行天数
  - 计算每日财务（收入、支出）
  - 更新员工工龄
  - 检查年初条件进行员工晋升（每年1月1日）
  - 检查月初条件扣除月工资（每月1日）
  - 计算满意度和声望值
  - 检查成就解锁条件
  - 触发随机事件（概率性）

### 2.2 财务管理系统

#### 2.2.1 收入来源
- **客房收入**：根据房间类型、数量和入住率计算
  - 计算公式：Σ(房间数量 × 房间价格 × 入住率)
- **餐饮收入**：基于入住客户数和员工数计算
  - 计算公式：入住客户数 × 20 × 酒店等级 + 员工数 × 5 × 酒店等级
- **康养部收入**：如果有康养部则产生额外收入
  - 计算公式：入住客户数 × 15 × 酒店等级（仅当康养部解锁时）
- **营销部收入**：营销活动带来的额外收入
  - 通过提升入住率间接增加客房收入

#### 2.2.2 支出项目
- **员工工资**：按月支付（每月1日自动扣除）
  - 基础工资 + 工龄加成（每年工龄+10%）
- **房间维护费用**：按月支付（每月1日自动扣除）
  - 每间房间每月100元维护费
- **营销活动费用**：按活动类型一次性支付
- **随机事件支出**：突发事件导致的额外支出
- **部门解锁费用**：解锁新部门时的一次性支出
- **酒店升级费用**：提升酒店等级时的一次性支出

#### 2.2.3 财务记录
- 记录所有收入和支出明细
- 按部门分类统计财务数据
- 按日期查看历史财务记录
- 提供月度、年度财务汇总报表
- 显示利润趋势图表

### 2.3 员工管理系统

#### 2.3.1 员工基本信息
- **姓名**：随机生成的中文姓名
- **所属部门**：员工工作的部门
- **等级**：初级、中级、高级、特级（影响服务能力和工资）
- **基础工资**：根据等级确定的基础薪资
- **实际工资**：基础工资 + 工龄加成（每年+10%）
- **入职日期**：员工加入酒店的日期
- **工龄**：员工在酒店工作的年数

#### 2.3.2 员工管理功能
- **招聘员工**：
  - 选择部门和等级进行招聘
  - 招聘费用：初级1万、中级3万、高级8万、特级20万
  - 需要对应部门已解锁
- **解雇员工**：
  - 可随时解雇任何员工
  - 解雇时需支付一个月工资作为补偿
- **员工晋升**：
  - 每年1月1日自动检查晋升条件
  - 工龄满3年且部门需要时自动晋升
  - 晋升后工资和服务能力提升
- **工资发放**：每月1日自动扣除所有员工工资

#### 2.3.3 员工等级与服务能力
- **初级员工**：服务能力1.0，基础工资3000元/月
- **中级员工**：服务能力1.5，基础工资5000元/月
- **高级员工**：服务能力2.0，基础工资8000元/月
- **特级员工**：服务能力3.0，基础工资15000元/月

#### 2.3.4 部门繁忙度计算
- 繁忙度 = 当日入住客户数 ÷ 部门员工总服务能力
- 繁忙度 > 100% 时显示为红色警告
- 繁忙度影响客户满意度和服务质量

### 2.4 部门管理系统

#### 2.4.1 部门基本信息
酒店包含以下11个部门，每个部门都有特定的功能和解锁条件：
- **前台部**：负责客户接待和入住登记
- **客房部**：负责客房清洁和维护
- **人事部**：负责员工管理和招聘
- **营销部**：负责市场推广和广告投放
- **餐饮部**：负责餐厅运营，提供额外收入
- **安保部**：负责酒店安全，提升客户满意度
- **财务部**：负责财务管理，优化成本控制
- **商务部**：负责商务接待，提升高端客户满意度
- **工程部**：负责设施维护，降低维护成本
- **康养部**：提供康养服务，产生额外收入
- **董事会**：最高管理层，全面提升酒店运营效率

#### 2.4.2 部门解锁规则（修正版）
- **1星酒店**：前台部、客房部、人事部（默认解锁）
- **2星酒店**：营销部（解锁费用：50万元）
- **3星酒店**：餐饮部（解锁费用：100万元）
- **4星酒店**：安保部（解锁费用：200万元）
- **5星酒店**：财务部（解锁费用：500万元）
- **6星酒店**：商务部（解锁费用：1000万元）
- **7星酒店**：工程部（解锁费用：3000万元）
- **8星酒店**：康养部（解锁费用：6000万元）
- **9星酒店**：董事会（解锁费用：10000万元）

#### 2.4.3 部门特殊效果
- **营销部**：可发起营销活动，提升入住率
- **餐饮部**：产生餐饮收入
- **安保部**：提升客户满意度+5分
- **财务部**：降低所有支出5%
- **商务部**：高端房间入住率+10%
- **工程部**：房间维护费用减少50%
- **康养部**：产生康养收入，满意度+10分
- **董事会**：所有收入增加10%

### 2.5 房间管理系统

#### 2.5.1 房间基本信息
- 房间具有类型、数量、价格等属性
- 不同星级酒店可解锁不同房间类型
- 房间类型包括：单人间、标准间、大床房、家庭房、商务间、行政间、豪华间、总统套房、皇家套房、总统别墅、皇宫套房

#### 2.5.2 房间解锁规则（修正版）
- **1星酒店**：单人间、标准间（初始各10间）
- **2星酒店**：大床房
- **3星酒店**：家庭房
- **4星酒店**：商务间
- **5星酒店**：行政间
- **6星酒店**：豪华间
- **7星酒店**：总统套房
- **8星酒店**：皇家套房
- **9星酒店**：总统别墅、皇宫套房

#### 2.5.3 房间价格规则（修正版）
- **单人间**：300元/晚（基础房型）
- **标准间**：500元/晚（基础房型）
- **大床房**：700元/晚
- **家庭房**：1000元/晚
- **商务间**：1500元/晚
- **行政间**：2000元/晚
- **豪华间**：3000元/晚
- **总统套房**：5000元/晚
- **皇家套房**：8000元/晚
- **总统别墅**：15000元/晚
- **皇宫套房**：30000元/晚

#### 2.5.4 房间管理功能
- **增加房间**：选择房间类型和数量进行建设
- **建设成本**：房间价格的10倍作为建设成本
- **维护费用**：每间房间每月100元维护费
- **入住率影响**：房间等级越高，基础入住率越低，但单价更高

### 2.6 成就系统

#### 2.6.1 成就分类
- **财务成就**：与资金管理相关的成就
  - 初次盈利、百万富翁、千万富翁、亿万富翁等
- **员工成就**：与员工管理相关的成就
  - 招聘达人、人才培养师、员工满百等
- **发展成就**：与酒店发展相关的成就
  - 星级提升、部门解锁、房间扩建等
- **经营成就**：与酒店经营相关的成就
  - 满意度达标、声望提升、连续盈利等
- **特殊成就**：特殊条件解锁的成就
  - 时间里程碑、随机事件相关等

#### 2.6.2 成就奖励机制
- 成就解锁后可领取声望值奖励
- 奖励数量根据成就难度确定：
  - 简单成就：50-100声望值
  - 中等成就：200-500声望值
  - 困难成就：1000-2000声望值
  - 传奇成就：5000+声望值
- 成就奖励只能领取一次
- 部分成就解锁后会自动领取奖励

#### 2.6.3 成就检查机制
- 每日时间推进时自动检查成就条件
- 成就解锁时显示通知提醒
- 成就页面显示进度和完成状态

### 2.7 营销系统

#### 2.7.1 营销活动类型
- **网络广告**：
  - 效果：提升入住率5%
  - 持续时间：7天
  - 费用：5万元
  - 适用：所有房间类型
- **电视广告**：
  - 效果：提升入住率10%
  - 持续时间：15天
  - 费用：20万元
  - 适用：所有房间类型
- **线下推广**：
  - 效果：提升入住率15%
  - 持续时间：30天
  - 费用：50万元
  - 适用：中低端房间类型
- **商务活动**：
  - 效果：提升入住率20%
  - 持续时间：30天
  - 费用：100万元
  - 适用：高端房间类型

#### 2.7.2 营销效果机制
- 营销活动通过提升入住率间接增加收入
- 多个营销活动效果可以叠加
- 营销活动有持续时间限制，到期后效果消失
- 营销部门解锁后才能发起营销活动
- 同一时间可以进行多个不同类型的营销活动

#### 2.7.3 营销活动管理
- 显示当前进行中的营销活动及剩余天数
- 显示营销活动的投资回报率统计
- 提供营销活动历史记录查询

### 2.8 随机事件系统

#### 2.8.1 事件类型
- **正面事件**：
  - 旅游旺季：入住率提升20%，持续7天
  - 好评如潮：声望值+100
  - 员工表彰：员工满意度提升
- **负面事件**：
  - 设备故障：维修费用5-20万元
  - 客户投诉：满意度下降10-20分
  - 员工离职：随机解雇1-3名员工
- **中性事件**：
  - 政策调整：影响某些收入或支出
  - 市场变化：影响特定房间类型的入住率

#### 2.8.2 事件触发机制
- 每日有5%概率触发随机事件
- 事件类型根据当前酒店状况和等级确定
- 部分事件需要玩家选择应对方案
- 事件效果可能是即时的或持续性的

## 3. 计算规则

### 3.1 入住率计算方法（修正版）
入住率根据房间类型的基础入住率范围随机计算：

#### 3.1.1 基础入住率范围
- **单人间**：60% ± 20%（40%-80%）
- **标准间**：70% ± 20%（50%-90%）
- **大床房**：75% ± 20%（55%-95%）
- **家庭房**：65% ± 20%（45%-85%）
- **商务间**：80% ± 20%（60%-100%）
- **行政间**：85% ± 20%（65%-100%）
- **豪华间**：90% ± 20%（70%-100%）
- **总统套房**：95% ± 20%（75%-100%）
- **皇家套房**：92% ± 20%（72%-100%）
- **总统别墅**：94% ± 20%（74%-100%）
- **皇宫套房**：96% ± 20%（76%-100%）

#### 3.1.2 入住率影响因素
- **营销活动效果**：根据活动类型提升5%-20%
- **客户满意度**：满意度每10分影响入住率±2%
- **酒店声望等级**：声望等级每级提升入住率1%
- **部门服务质量**：部门繁忙度影响服务质量
- **随机事件**：特殊事件可能大幅影响入住率
- **季节性因素**：预留接口，暂未实现

#### 3.1.3 客户数量计算
- 入住客户总数 = Σ(各房间数量 × 各房间入住率)
- 入住率最终限制在0%-100%之间
### 3.2 满意度计算方法（修正版）
客户满意度基于以下因素计算：

#### 3.2.1 基础满意度构成
1. **基础满意度**：50分
2. **房间等级加成**：
   - 皇宫套房：+10分
   - 总统别墅：+8分
   - 皇家套房：+6分
   - 总统套房：+4分
   - 豪华间：+2分
   - 其他房间：+0分
3. **酒店星级加成**：每星+1分（最高+9分）
4. **部门服务影响**：
   - 安保部：+5分（如已解锁）
   - 康养部：+10分（如已解锁）
   - 其他部门：通过繁忙度间接影响
5. **部门繁忙度影响**：
   - 平均繁忙度 < 50%：+10分
   - 平均繁忙度 50%-80%：+0分
   - 平均繁忙度 80%-100%：-5分
   - 平均繁忙度 > 100%：-15分
6. **随机事件影响**：±5-20分

#### 3.2.2 满意度对声望的影响
- 满意度 >= 90：声望值 +5/天
- 满意度 >= 80：声望值 +3/天
- 满意度 >= 70：声望值 +1/天
- 满意度 60-69：声望值 +0/天
- 满意度 50-59：声望值 -1/天
- 满意度 40-49：声望值 -3/天
- 满意度 <= 39：声望值 -5/天

#### 3.2.3 满意度限制
- 满意度最终限制在0-100分之间
- 满意度影响客户入住意愿和声望值变化

### 3.3 声望值计算方法（修正版）
声望值基于以下因素每日变化：

#### 3.3.1 声望值变化因素
1. **每日基础变化**：根据满意度计算（见3.2.2）
2. **财务表现奖励**：
   - 日利润 > 10万：声望值 +3
   - 日利润 > 5万：声望值 +2
   - 日利润 > 1万：声望值 +1
   - 日利润 < 0（亏损）：声望值 -1
3. **成就解锁奖励**：解锁成就时获得一次性声望值奖励
4. **酒店升级奖励**：每升一级获得100×等级的声望值
5. **随机事件影响**：±10-100声望值

#### 3.3.2 声望等级划分（修正版）
- **0-499**：默默无闻
- **500-1999**：小有名气
- **2000-4999**：知名酒店
- **5000-9999**：著名品牌
- **10000-19999**：行业标杆
- **20000-49999**：行业领袖
- **50000-99999**：国际知名
- **100000+**：传奇酒店

#### 3.3.3 声望等级效果
- 声望等级影响入住率（每级+1%）
- 声望等级影响员工招聘成功率
- 声望等级影响随机事件的正面概率
- 高声望等级解锁特殊成就

### 3.4 收益计算方法（修正版）

#### 3.4.1 每日收入构成
1. **客房收入** = Σ(房间数量 × 房间价格 × 入住率)
2. **餐饮收入** = 入住客户数 × 20 × 酒店等级 + 员工数 × 5 × 酒店等级
   - 需要餐饮部解锁才能产生餐饮收入
3. **康养收入** = 入住客户数 × 15 × 酒店等级
   - 需要康养部解锁才能产生康养收入
4. **董事会加成** = 总收入 × 10%
   - 需要董事会解锁才能获得加成

#### 3.4.2 收入优化因素
- 营销活动通过提升入住率间接增加客房收入
- 部门解锁带来新的收入来源
- 酒店等级提升增加单位收入
- 房间类型升级提高单价收入

### 3.5 支出计算方法（修正版）

#### 3.5.1 每日支出构成
1. **员工工资**：每月1日扣除
   - 计算公式：基础工资 × (1 + 工龄 × 0.1)
2. **房间维护费**：每月1日扣除
   - 计算公式：房间总数 × 100元/月
   - 工程部解锁后减少50%
3. **营销活动费用**：发起活动时一次性扣除
4. **随机事件支出**：突发事件导致的额外支出
5. **部门解锁费用**：解锁部门时一次性支出
6. **酒店升级费用**：升级酒店等级时一次性支出

#### 3.5.2 支出优化因素
- 财务部解锁后所有支出减少5%
- 工程部解锁后维护费用减少50%
- 合理的员工配置可以避免过度招聘

### 3.6 酒店升级条件

#### 3.6.1 升级要求
每个等级的升级都需要满足以下条件：
- **资金要求**：拥有足够的升级费用
- **声望要求**：达到对应的声望值门槛
- **经营天数要求**：运营达到一定天数
- **满意度要求**：客户满意度达到标准

#### 3.6.2 各等级升级条件
- **2星**：费用100万，声望500，运营30天，满意度60
- **3星**：费用300万，声望1500，运营90天，满意度65
- **4星**：费用800万，声望3000，运营180天，满意度70
- **5星**：费用2000万，声望6000，运营365天，满意度75
- **6星**：费用5000万，声望12000，运营730天，满意度80
- **7星**：费用1亿，声望25000，运营1095天，满意度85
- **8星**：费用2亿，声望50000，运营1460天，满意度90
- **9星**：费用5亿，声望100000，运营1825天，满意度95

## 4. 用户界面设计（已优化实现）

### 4.1 主页面布局结构
- **页面标题区域**：
  - 酒店名称显示（可点击修改）
  - 系统标识和品牌信息

- **酒店基本信息卡片**：
  - 第一行：酒店等级、资金余额、当前日期、运营天数、声望值、声望等级
  - 第二行：客户满意度（带可视化进度条）、时间控制区域

- **时间控制系统**：
  - 时间状态显示（运行中/已暂停）
  - 时间速度显示（1倍速/2倍速）
  - 暂停/继续按钮、切换速度按钮、推进一天按钮

- **运营状况概览卡片**：
  - 房间总数、客户总数、员工总数、月度利润
  - 每项配有对应图标和颜色区分

### 4.2 游戏管理功能区域
- **核心功能**：
  - 保存游戏（支持自定义存档名称）
  - 读取存档（预留功能接口）
  - 重新开始游戏（带确认机制）
  - 游戏帮助（详细说明文档）
- **扩展功能**：
  - 导出数据（预留数据备份功能）
  - 游戏设置（预留个性化设置）

### 4.3 快捷管理操作区域
- **管理模块导航**：
  - 部门管理、员工管理、房间管理
  - 财务管理、酒店升级、营销管理
  - 统一的按钮样式和图标系统

### 4.4 数据可视化区域
- **部门状态与繁忙度**：
  - 所有部门的解锁状态显示
  - 已解锁部门的繁忙度进度条
  - 未解锁部门的解锁费用信息

- **入住率数据表格**：
  - 最近10天的详细入住率数据
  - 颜色编码：绿色(≥80%)、黄色(50-79%)、红色(<50%)
  - 按房间类型分行显示

### 4.5 交互体验设计
- **即时反馈系统**：
  - 成功操作：绿色提示消息
  - 错误操作：红色错误提示
  - 信息提示：蓝色信息消息
  - 自动消失机制（3秒后自动隐藏）

- **确认机制**：
  - 危险操作（重新开始游戏）需要用户确认
  - 重要操作（修改酒店名称）提供输入验证
  - 资金不足等情况提供明确的错误说明

- **实时更新机制**：
  - 每5秒自动刷新所有数据
  - 用户操作后立即更新相关数据
  - 页面加载完成后立即获取最新数据

### 4.6 响应式设计适配
- **桌面端（≥1200px）**：完整的多列布局，所有功能完全展示
- **平板端（768px-1199px）**：适当调整列数，保持功能完整性
- **移动端（<768px）**：单列布局，优化触摸操作体验

### 4.7 视觉设计规范
- **色彩方案**：使用Bootstrap主题色彩系统
- **图标系统**：统一使用Bootstrap Icons
- **布局方式**：卡片式布局，清晰的信息分组
- **字体排版**：层次分明的标题和内容结构

### 4.8 性能优化措施
- **代码优化**：使用原生JavaScript，移除jQuery依赖
- **网络优化**：合并API请求，减少网络开销
- **DOM优化**：减少重绘和重排操作
- **缓存策略**：适当的数据缓存机制

## 5. 游戏平衡性设计

### 5.1 难度曲线设计
- **初期（1-3星）**：资金相对充裕，主要学习游戏机制
- **中期（4-6星）**：资金压力增大，需要合理规划投资
- **后期（7-9星）**：高投入高回报，考验长期经营策略

### 5.2 经济平衡
- **收入增长**：随酒店等级和房间档次提升而增长
- **支出控制**：通过部门解锁获得成本优化能力
- **投资回报**：营销活动和设施投资需要合理的回报周期

### 5.3 时间平衡
- **升级节奏**：每个等级需要合理的时间积累
- **事件频率**：随机事件不会过于频繁影响正常经营
- **成就解锁**：成就解锁有合理的时间分布

## 6. 技术需求

### 6.1 性能要求
- **响应时间**：页面加载时间不超过2秒
- **数据处理**：时间推进计算在1秒内完成
- **内存使用**：单机运行内存占用不超过500MB

### 6.2 兼容性要求
- **浏览器支持**：Chrome、Firefox、Safari、Edge最新版本
- **操作系统**：Windows 10+、macOS 10.14+、Ubuntu 18.04+
- **屏幕分辨率**：最低支持1024×768

### 6.3 数据安全
- **数据备份**：支持游戏数据导出和导入
- **数据完整性**：防止数据损坏和丢失
- **操作日志**：记录关键操作用于问题排查

## 7. 扩展性设计

### 7.1 功能扩展
- **季节系统**：预留季节性因素影响接口
- **竞争对手**：预留AI竞争对手系统接口
- **多酒店管理**：预留酒店集团管理功能接口

### 7.2 数据扩展
- **自定义规则**：支持通过配置文件调整游戏参数
- **多语言支持**：预留国际化接口
- **主题定制**：支持界面主题切换

## 8. 测试需求

### 8.1 功能测试
- **核心功能**：时间推进、财务计算、员工管理等核心功能
- **边界条件**：极值情况下的系统稳定性
- **异常处理**：错误输入和异常情况的处理

### 8.2 性能测试
- **长时间运行**：连续运行24小时的稳定性测试
- **大数据量**：大量员工和房间情况下的性能测试
- **内存泄漏**：长期运行的内存使用情况监控

### 8.3 用户体验测试
- **易用性测试**：新用户上手难度评估
- **界面测试**：不同分辨率下的界面适配
- **操作流程测试**：常用操作流程的便捷性测试